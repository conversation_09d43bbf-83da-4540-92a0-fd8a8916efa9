"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Provider as <PERSON><PERSON>Provider } from "jotai";
import React from "react";

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchInterval: false,
			refetchOnWindowFocus: false,
			retry: false,
		},
	},
});

export const GlobalProviders = ({ children }: { children: React.ReactNode }) => {
	return (
		<JotaiProvider>
			<QueryClientProvider client={queryClient}>
				{children}
				<ReactQueryDevtools initialIsOpen={false} />
			</QueryClientProvider>
		</JotaiProvider>
	);
};
