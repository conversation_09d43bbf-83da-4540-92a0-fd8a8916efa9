import { DependencyList, useCallback, useEffect, useRef } from "react";

export const useDebounce = (callback: () => void, delay: number, deps: DependencyList = []) => {
	const handler = useRef<number | undefined>(undefined);

	const debouncedCallback = useCallback(() => {
		if (handler.current) {
			clearTimeout(handler.current);
		}
		handler.current = window.setTimeout(() => {
			callback();
		}, delay);
	}, [callback, delay, ...deps]);

	useEffect(() => {
		return () => {
			if (handler.current) {
				clearTimeout(handler.current);
			}
		};
	}, []);

	return debouncedCallback;
};
