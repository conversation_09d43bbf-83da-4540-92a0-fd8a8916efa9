import { Writable<PERSON>tom, useAtom } from "jotai";

export interface ModalController {
	isOpen: boolean;
	open: () => void;
	close: () => void;
	handleOpen: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

export const useModal = (modalAtom: WritableAtom<boolean, [boolean], void>): ModalController => {
	const [isOpen, setIsOpen] = useAtom(modalAtom);

	const open = (): void => setIsOpen(true);
	const close = (): void => setIsOpen(false);
	const handleOpen = (e: React.MouseEvent<HTMLButtonElement>): void => {
		e.preventDefault();
		open();
	};

	return { isOpen, open, close, handleOpen };
};
