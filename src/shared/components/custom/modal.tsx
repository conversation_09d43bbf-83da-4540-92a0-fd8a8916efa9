"use client";

import React, { useEffect, useRef } from "react";
import { createPortal } from "react-dom";

interface IModalProps {
	isOpen: boolean;
	onRequestClose: () => void;
	shouldCloseOnOverlayClick?: boolean;
	className?: string;
	overlayClassName?: string;
	children: React.ReactNode;
}

export const Modal = ({ isOpen, onRequestClose, shouldCloseOnOverlayClick, className, overlayClassName, children }: IModalProps) => {
	const modalRef = useRef<HTMLDivElement>(null);
	const previousActiveElement = useRef<HTMLElement | null>(null);

	useEffect(() => {
		if (isOpen) {
			previousActiveElement.current = document.activeElement as HTMLElement;
			document.body.style.overflow = "hidden";

			setTimeout(() => {
				modalRef.current?.focus();
			}, 0);
		} else {
			document.body.style.overflow = "";
			previousActiveElement.current?.focus();
		}

		return () => {
			document.body.style.overflow = "";
		};
	}, [isOpen]);

	const handleKeyDown = React.useCallback(
		(event: KeyboardEvent) => {
			if (event.key === "Escape") {
				onRequestClose();
			} else if (event.key === "Tab") {
				const focusableElements = modalRef.current?.querySelectorAll<HTMLElement>(
					'a[href], button:not([disabled]), textarea, input[type="text"], input[type="radio"], input[type="checkbox"], select'
				);

				if (focusableElements && focusableElements.length > 0) {
					const firstElement = focusableElements[0];
					const lastElement = focusableElements[focusableElements.length - 1];

					if (!event.shiftKey && document.activeElement === lastElement) {
						event.preventDefault();
						firstElement.focus();
					} else if (event.shiftKey && document.activeElement === firstElement) {
						event.preventDefault();
						lastElement.focus();
					}
				}
			}
		},
		[onRequestClose]
	);

	useEffect(() => {
		if (isOpen) {
			document.addEventListener("keydown", handleKeyDown);
		} else {
			document.removeEventListener("keydown", handleKeyDown);
		}

		return () => {
			document.removeEventListener("keydown", handleKeyDown);
		};
	}, [isOpen, handleKeyDown]);

	const handleOverlayClick = (event: React.MouseEvent) => {
		if (shouldCloseOnOverlayClick && event.target === event.currentTarget) {
			onRequestClose();
		}
	};

	if (!isOpen) return null;

	return createPortal(
		<div
			id="modal"
			role="dialog"
			aria-labelledby="modal-title"
			aria-modal="true"
			className={`fixed inset-0 z-50 flex items-center justify-center bg-black/50 transition-opacity duration-200 ${overlayClassName} ${
				isOpen ? "opacity-100" : "opacity-0"
			}`}
			onClick={handleOverlayClick}
		>
			<div
				className={`relative transform scale-90 opacity-0 rounded-[10px] bg-white transition-transform duration-200 ${className} ${
					isOpen ? "scale-100 opacity-100" : "scale-90 opacity-0"
				}`}
				ref={modalRef}
				tabIndex={-1}
			>
				{children}
			</div>

			{/* <Toaster richColors closeButton position="top-right" /> */}
		</div>,
		document.body
	);
};
