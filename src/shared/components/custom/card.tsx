import { LucideProps } from "lucide-react";
import { ForwardRefExoticComponent, RefAttributes } from "react";
import { Separator } from "../ui/separator";

export const Card = ({
	Icon,
	title,
	children,
}: {
	Icon: ForwardRefExoticComponent<Omit<LucideProps, "ref"> & RefAttributes<SVGSVGElement>>;
	title: string;
	children: React.ReactNode;
}) => (
	<div className="w-full flex flex-col h-full">
		<div className="h-full bg-white p-3 rounded-lg shadow-lg flex flex-col">
			<header className="w-full flex flex-col">
				<div className="flex gap-5 items-center">
					{Icon && <Icon className="w-6 h-6 text-gray-600 hover:text-gray-800 cursor-pointer" aria-label={title} />}
					<h1 className="text-2xl font-semibold text-center text-gray-800">{title}</h1>
				</div>
				<Separator orientation="horizontal" className="my-4" />
			</header>
			<main className="w-full flex flex-col">{children}</main>
		</div>
	</div>
);
