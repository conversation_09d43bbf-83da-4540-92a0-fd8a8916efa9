"use client";

import { ActionButtons } from "@/modules/presential/components/terms/components/action-buttons";
import { TagsList } from "@/modules/presential/components/terms/lists/tags-list";
import { TermsList } from "@/modules/presential/components/terms/lists/terms-list";
import { CreateTagModal } from "@/modules/presential/components/terms/modals/create-tag.modal";
import { CreateTermModal } from "@/modules/presential/components/terms/modals/create-term-modal";
import { EditTermSheet } from "@/modules/presential/components/terms/modals/edit-term-modal";
import { useTermsPage } from "@/modules/presential/hooks/terms/use-terms-page.hook";
import { FileText } from "lucide-react";

const TermosPage = () => {
	const {
		selectedTermId,
		isCreateTermOpen,
		isCreateTagOpen,
		isEditTermOpen,
		editTermData,
		setIsCreateTermOpen,
		setIsCreateTag<PERSON><PERSON>,
		setIsEditTermOpen,
		setSelectedTermId,
		handleSelectTermForEdit,
	} = useTermsPage();

	return (
		<div className=" md:p-6  space-y-2 md:space-y-6">
			<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-4">
					<div className="flex items-center gap-3 md:gap-4">
						<div className="bg-gray-100 p-2 md:p-3 rounded-lg flex items-center justify-center">
							<FileText className="w-6 h-6 md:w-8 md:h-8 text-primary" />
						</div>
						<div>
							<h1 className="text-xl md:text-2xl font-semibold text-gray-900">Gerenciar Termos</h1>
							<p className="text-sm md:text-base text-gray-500">Crie, edite e gerencie os termos da plataforma</p>
						</div>
					</div>
					<div className="w-full md:w-auto mt-3 md:mt-0 flex justify-end">
						<ActionButtons onCreateTerm={() => setIsCreateTermOpen(true)} onCreateTag={() => setIsCreateTagOpen(true)} />
					</div>
				</div>
				<div className="flex flex-wrap gap-2 mt-4 w-full justify-center md:justify-end">
					<CreateTagModal isOpen={isCreateTagOpen} onOpenChange={setIsCreateTagOpen} onClose={() => setIsCreateTagOpen(false)} />
					<CreateTermModal isOpen={isCreateTermOpen} onOpenChange={setIsCreateTermOpen} onClose={() => setIsCreateTermOpen(false)} />
					{selectedTermId && (
						<EditTermSheet
							isOpen={isEditTermOpen}
							onOpenChange={setIsEditTermOpen}
							selectedTermId={selectedTermId}
							initialData={editTermData}
							onClose={() => setSelectedTermId(null)}
						/>
					)}
				</div>
			</div>
			<div className="flex flex-col bg-white rounded-lg shadow-lg justify-between items-center">
				<TermsList onEditTerm={handleSelectTermForEdit} />
			</div>
			<div className="flex flex-col bg-white rounded-lg shadow-lg justify-between items-center">
				<TagsList openTagModal={() => setIsCreateTagOpen(true)} />
			</div>
		</div>
	);
};

export default TermosPage;
