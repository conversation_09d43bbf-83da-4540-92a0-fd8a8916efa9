"use client";
import { EpiReturnFilters } from "@/modules/presential/components/epi/list/epi-return-filters";
import { SignatureReturnEpiList } from "@/modules/presential/components/epi/list/signature-return-epi-list";
import { useEpiReturnFilters } from "@/modules/presential/hooks/epi/use-epi-return-filters.hook";
import { HardHat } from "lucide-react";

const EPIPage = () => {
	const { form, hasActiveFilters } = useEpiReturnFilters();

	return (
		<div className=" md:p-6  space-y-2 md:space-y-6">
			<div className="flex flex-col p-3 md:p-6 bg-white shadow-lg rounded-lg">
				<div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-3 md:gap-4">
					<div className="flex items-center gap-3 md:gap-4">
						<div className="bg-gray-100 p-2 md:p-3 rounded-lg flex items-center justify-center">
							<HardHat className="w-6 h-6 md:w-8 md:h-8 text-primary" />
						</div>
						<div>
							<h1 className="text-xl md:text-2xl font-semibold text-gray-900">Gerenciar Assinaturas de EPI</h1>
							<p className="text-sm md:text-base text-gray-500">Gerencie assinaturas de retirada e devolução de EPI na plataforma</p>
						</div>
					</div>
				</div>
			</div>

			<div className="flex flex-col bg-white rounded-lg shadow-lg justify-between items-center">
				<EpiReturnFilters form={form} hasActiveFilters={hasActiveFilters()} />
			</div>

			<div className="flex flex-col bg-white rounded-lg shadow-lg justify-between items-center">
				<SignatureReturnEpiList form={form} />
			</div>
		</div>
	);
};

export default EPIPage;
