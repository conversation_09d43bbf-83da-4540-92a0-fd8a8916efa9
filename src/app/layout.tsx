import type { <PERSON><PERSON><PERSON> } from "next";

import { GlobalProviders } from "@/shared/providers";
import { Montserrat } from "next/font/google";
import { Toaster } from "react-hot-toast";
import "./globals.css";

const montserrat = Montserrat({
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: "Pormade Assinaturas",
	description: "Sistema de assinatura eletrônica da Pormade",
	icons: {
		icon: "/icon.png",
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="pt-BR">
			<head>
				<link rel="icon" href="/icon.png" type="image/png" />
			</head>
			<body className={`min-h-screen h-full bg-gradient-to-tr from-gray-100 to-gray-200 ${montserrat.className}`}>
				<GlobalProviders>{children}</GlobalProviders>
				<Toaster position="top-right" />
			</body>
		</html>
	);
}
