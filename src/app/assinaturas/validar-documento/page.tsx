"use client";
import { DocumentValidated } from "@/modules/signature/components/validate-document/document-validated";
import { UploadDocument } from "@/modules/signature/components/validate-document/upload-document";
import { useFileUpload } from "@/modules/signature/hooks/signature/validate-document/file-upload.hook";
import { useValidateDocument } from "@/modules/signature/hooks/signature/validate-document/validate-document.hook";
import { DocumentValidationService } from "@/modules/signature/services/manage/manage-validation-document";

const validationService = new DocumentValidationService();

const ValidateDocumentPage = () => {
	const { file, isDragging, handleFileChange, handleDrop, handleDragOver, handleDragLeave } = useFileUpload();
	const { isValidDocument, handleUpload, setIsValidDocument, isPending } = useValidateDocument(validationService);

	return (
		<>
			{!isValidDocument ? (
				<UploadDocument
					isPending={isPending}
					file={file}
					isDragging={isDragging}
					handleFileChange={handleFileChange}
					handleDrop={handleDrop}
					handleDragOver={handleDragOver}
					handleDragLeave={handleDragLeave}
					handleUpload={handleUpload}
				/>
			) : (
				<DocumentValidated data={isValidDocument} setIsValid={() => setIsValidDocument(null)} />
			)}
		</>
	);
};

export default ValidateDocumentPage;
