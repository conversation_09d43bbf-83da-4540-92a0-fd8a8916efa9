import { IUser } from "@/modules/presential/types/user";
import Logo from "@/shared/assets/logos/logo-admin-preta.png";
import { HardHat } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { FiChevronDown, FiFileText, FiLogOut, FiMenu, FiPlusCircle, FiUser, FiX } from "react-icons/fi";

interface AdminHeaderProps {
	user: IUser;
	onLogout?: () => void;
}

export const AdminHeader = ({ user, onLogout }: AdminHeaderProps) => {
	const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
	const pathname = usePathname();

	const getInitials = (name: string) => {
		return name
			.split(" ")
			.map(n => n[0])
			.join("")
			.toUpperCase()
			.slice(0, 2);
	};

	const isActiveRoute = (href: string) => {
		return pathname === href || pathname.startsWith(href + "/");
	};

	const navItems = [
		{
			label: "Termos",
			href: "/admin/termos",
			icon: FiFileText,
		},
		{
			label: "Nova Assinatura",
			href: "/admin/nova-assinatura",
			icon: FiPlusCircle,
		},
		{
			label: "EPI",
			href: "/admin/epi",
			icon: HardHat,
		},
	];

	return (
		<header className="bg-white border-b border-gray-200 sticky top-0 z-50">
			<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
				<div className="flex h-16 md:h-20 justify-between items-center">
					<Link href="/admin" className="flex-shrink-0">
						<Image src={Logo} alt="Logo da empresa" width={150} className="drop-shadow" />
					</Link>
					<div className="hidden md:flex items-center space-x-8">
						<nav className="flex items-center space-x-2">
							{navItems.map(item => {
								const isActive = isActiveRoute(item.href);
								return (
									<Link
										key={item.href}
										href={item.href}
										className={`flex items-center space-x-2 px-4 py-2.5 rounded-lg transition-colors ${
											isActive
												? "bg-pormade/10 text-pormade border border-pormade/20"
												: "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
										}`}
									>
										<item.icon className="w-4 h-4" />
										<span className="text-sm font-medium">{item.label}</span>
									</Link>
								);
							})}
						</nav>
						<div className="w-px h-8 bg-gray-200" />
						<div className="relative group">
							<button className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors">
								<div className="flex items-center space-x-3">
									<div className="w-9 h-9 rounded-full bg-gradient-to-br from-gray-800 to-gray-600 flex items-center justify-center text-white text-sm font-medium">
										{getInitials(user.name)}
									</div>
									<div className="text-left">
										<p className="text-sm font-medium text-gray-900">{user.name}</p>
									</div>
									<FiChevronDown className="w-4 h-4 text-gray-400" />
								</div>
							</button>
							<div className="absolute right-0 mt-1 w-48 bg-white rounded-lg shadow-lg py-1 border border-gray-100 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-200">
								<Link href="/admin/perfil" className="flex items-center space-x-3 px-4 py-2.5 text-sm text-gray-700 hover:bg-gray-50">
									<FiUser className="w-4 h-4" />
									<span>Meu Perfil</span>
								</Link>
								<div className="h-px bg-gray-200" />
								<button
									onClick={onLogout}
									className="w-full flex items-center space-x-3 px-4 py-2.5 text-sm text-red-600 hover:bg-red-50"
								>
									<FiLogOut className="w-4 h-4" />
									<span>Sair</span>
								</button>
							</div>
						</div>
					</div>

					<div className="flex items-center space-x-4 md:hidden">
						<div className="w-8 h-8 rounded-full bg-gradient-to-br from-gray-800 to-gray-600 flex items-center justify-center text-white text-sm font-medium">
							{getInitials(user.name)}
						</div>
						<button
							onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
							className="p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
						>
							{isMobileMenuOpen ? <FiX className="w-6 h-6" /> : <FiMenu className="w-6 h-6" />}
						</button>
					</div>
				</div>
			</div>
			{isMobileMenuOpen && (
				<div className="md:hidden border-t border-gray-200">
					<div className="px-4 py-3 space-y-1">
						{navItems.map(item => {
							const isActive = isActiveRoute(item.href);
							return (
								<Link
									key={item.href}
									href={item.href}
									className={`flex items-center space-x-2 px-4 py-3 rounded-lg transition-colors ${
										isActive
											? "bg-pormade/10 text-pormade border border-pormade/20"
											: "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
									}`}
									onClick={() => setIsMobileMenuOpen(false)}
								>
									<item.icon className="w-5 h-5" />
									<span className="text-base font-medium">{item.label}</span>
								</Link>
							);
						})}
						<div className="h-px bg-gray-200 my-2" />
						<Link
							href="/admin/perfil"
							className="flex items-center space-x-2 px-4 py-3 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors"
							onClick={() => setIsMobileMenuOpen(false)}
						>
							<FiUser className="w-5 h-5" />
							<span className="text-base font-medium">Meu Perfil</span>
						</Link>
						<button
							onClick={() => {
								setIsMobileMenuOpen(false);
								onLogout?.();
							}}
							className="w-full flex items-center space-x-2 px-4 py-3 rounded-lg text-red-600 hover:bg-red-50 transition-colors"
						>
							<FiLogOut className="w-5 h-5" />
							<span className="text-base font-medium">Sair</span>
						</button>
					</div>
				</div>
			)}
		</header>
	);
};
