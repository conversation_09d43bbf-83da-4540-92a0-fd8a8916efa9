import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { Button } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { cn } from "@/shared/lib/utils";
import { Camera, Upload, User, X } from "lucide-react";
import { useCallback, useRef, useState } from "react";

export const PersonalDataStep = () => {
	const { signatureData, updateSignatureData } = useEpiSignaturePage();
	const [formData, setFormData] = useState({
		name: signatureData.personName || "",
		cpf: signatureData.personCpf || "",
	});
	const [photo, setPhoto] = useState<File | null>(signatureData.photo || null);
	const [photoPreview, setPhotoPreview] = useState<string | null>(null);
	const [errors, setErrors] = useState<Record<string, string>>({});

	const fileInputRef = useRef<HTMLInputElement>(null);
	const cameraInputRef = useRef<HTMLInputElement>(null);

	const validateCPF = (cpf: string): boolean => {
		const cleanCPF = cpf.replace(/\D/g, "");
		if (cleanCPF.length !== 11) return false;

		// Verificar se todos os dígitos são iguais
		if (/^(\d)\1{10}$/.test(cleanCPF)) return false;

		// Validar dígitos verificadores
		let sum = 0;
		for (let i = 0; i < 9; i++) {
			sum += parseInt(cleanCPF.charAt(i)) * (10 - i);
		}
		let remainder = (sum * 10) % 11;
		if (remainder === 10 || remainder === 11) remainder = 0;
		if (remainder !== parseInt(cleanCPF.charAt(9))) return false;

		sum = 0;
		for (let i = 0; i < 10; i++) {
			sum += parseInt(cleanCPF.charAt(i)) * (11 - i);
		}
		remainder = (sum * 10) % 11;
		if (remainder === 10 || remainder === 11) remainder = 0;
		if (remainder !== parseInt(cleanCPF.charAt(10))) return false;

		return true;
	};

	const formatCPF = (value: string): string => {
		const cleanValue = value.replace(/\D/g, "");
		return cleanValue
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d)/, "$1.$2")
			.replace(/(\d{3})(\d{1,2})/, "$1-$2")
			.replace(/(-\d{2})\d+?$/, "$1");
	};

	const handleInputChange = (field: string, value: string) => {
		let formattedValue = value;

		if (field === "cpf") {
			formattedValue = formatCPF(value);
		}

		setFormData(prev => ({ ...prev, [field]: formattedValue }));

		// Limpar erro quando o usuário começar a digitar
		if (errors[field]) {
			setErrors(prev => ({ ...prev, [field]: "" }));
		}

		// Atualizar dados no contexto
		updateSignatureData({
			[field === "name" ? "personName" : "personCpf"]: formattedValue,
		});
	};

	const handlePhotoUpload = useCallback(
		(file: File) => {
			if (file && file.type.startsWith("image/")) {
				setPhoto(file);

				const reader = new FileReader();
				reader.onload = e => {
					setPhotoPreview(e.target?.result as string);
				};
				reader.readAsDataURL(file);

				updateSignatureData({ photo: file });
			}
		},
		[updateSignatureData]
	);

	const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			handlePhotoUpload(file);
		}
	};

	const handleRemovePhoto = () => {
		setPhoto(null);
		setPhotoPreview(null);
		updateSignatureData({ photo: undefined });
		if (fileInputRef.current) fileInputRef.current.value = "";
		if (cameraInputRef.current) cameraInputRef.current.value = "";
	};

	const isFormValid = formData.name.trim().length >= 2 && validateCPF(formData.cpf) && photo !== null;

	return (
		<div className="space-y-6">
			<div className="text-center mb-6">
				<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Dados Pessoais</h2>
				<p className="text-sm md:text-base text-gray-600">Preencha os dados pessoais e adicione uma foto</p>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Formulário de Dados */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<User className="w-5 h-5" />
							Informações Pessoais
						</CardTitle>
						<CardDescription>Digite o nome completo e CPF da pessoa</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="space-y-2">
							<Label htmlFor="name">Nome Completo *</Label>
							<Input
								id="name"
								type="text"
								placeholder="Digite o nome completo"
								value={formData.name}
								onChange={e => handleInputChange("name", e.target.value)}
								className={cn("transition-colors", errors.name ? "border-red-500 focus:border-red-500" : "")}
							/>
							{errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
						</div>

						<div className="space-y-2">
							<Label htmlFor="cpf">CPF *</Label>
							<Input
								id="cpf"
								type="text"
								placeholder="000.000.000-00"
								value={formData.cpf}
								onChange={e => handleInputChange("cpf", e.target.value)}
								maxLength={14}
								className={cn("transition-colors", errors.cpf ? "border-red-500 focus:border-red-500" : "")}
							/>
							{errors.cpf && <p className="text-sm text-red-600">{errors.cpf}</p>}
						</div>
					</CardContent>
				</Card>

				{/* Upload de Foto */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Camera className="w-5 h-5" />
							Foto *
						</CardTitle>
						<CardDescription>Adicione uma foto da pessoa para identificação</CardDescription>
					</CardHeader>
					<CardContent>
						{photoPreview ? (
							<div className="space-y-4">
								<div className="relative">
									<img src={photoPreview} alt="Preview" className="w-full h-48 object-cover rounded-lg border" />
									<Button variant="destructive" size="icon" className="absolute top-2 right-2" onClick={handleRemovePhoto}>
										<X className="w-4 h-4" />
									</Button>
								</div>
								<div className="flex gap-2">
									<Button variant="outline" onClick={() => fileInputRef.current?.click()} className="flex-1">
										<Upload className="w-4 h-4 mr-2" />
										Trocar Foto
									</Button>
									<Button variant="outline" onClick={() => cameraInputRef.current?.click()} className="flex-1">
										<Camera className="w-4 h-4 mr-2" />
										Câmera
									</Button>
								</div>
							</div>
						) : (
							<div className="space-y-4">
								<div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
									<Camera className="w-12 h-12 text-gray-400 mx-auto mb-4" />
									<p className="text-sm text-gray-600 mb-4">Nenhuma foto selecionada</p>
									<div className="flex flex-col sm:flex-row gap-2">
										<Button variant="outline" onClick={() => fileInputRef.current?.click()} className="flex-1">
											<Upload className="w-4 h-4 mr-2" />
											Escolher Arquivo
										</Button>
										<Button variant="outline" onClick={() => cameraInputRef.current?.click()} className="flex-1">
											<Camera className="w-4 h-4 mr-2" />
											Usar Câmera
										</Button>
									</div>
								</div>
							</div>
						)}

						{/* Inputs ocultos para upload */}
						<input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileSelect} className="hidden" />
						<input ref={cameraInputRef} type="file" accept="image/*" capture="user" onChange={handleFileSelect} className="hidden" />
					</CardContent>
				</Card>
			</div>

			{/* Status de Validação */}
			<Card className={cn("border-2", isFormValid ? "border-green-200 bg-green-50" : "border-yellow-200 bg-yellow-50")}>
				<CardContent className="pt-6">
					<div className="flex items-center gap-2">
						<div className={cn("w-3 h-3 rounded-full", isFormValid ? "bg-green-500" : "bg-yellow-500")} />
						<span className={cn("text-sm font-medium", isFormValid ? "text-green-800" : "text-yellow-800")}>
							{isFormValid ? "Todos os dados foram preenchidos corretamente" : "Preencha todos os campos obrigatórios"}
						</span>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};
