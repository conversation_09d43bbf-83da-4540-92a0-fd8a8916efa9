import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Separator } from "@/shared/components/ui/separator";
import { cn } from "@/shared/lib/utils";
import { Check, Minus, Plus, Shield, Users } from "lucide-react";
import { useState } from "react";

interface IEpiItem {
	id: number;
	name: string;
	description?: string;
	category?: string;
	isSelected: boolean;
	quantity: number;
	needsReturn: boolean;
}

// Mock data - substituir por hook real
const MOCK_EPIS: IEpiItem[] = [
	{ id: 1, name: "Capacete de Segurança", description: "Proteção craniana", category: "Cabe<PERSON>", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 2, name: "Óculos de Proteção", description: "Proteção ocular", category: "Olhos", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 3, name: "Luvas de Segurança", description: "Proteção das mãos", category: "Mãos", isSelected: false, quantity: 1, needsReturn: false },
	{ id: 4, name: "Botina de Segurança", description: "Proteção dos pés", category: "Pés", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 5, name: "Colete Refletivo", description: "Sinalização e proteção", category: "Tronco", isSelected: false, quantity: 1, needsReturn: true },
	{ id: 6, name: "Protetor Auricular", description: "Proteção auditiva", category: "Ouvidos", isSelected: false, quantity: 1, needsReturn: false },
];

export const EpiSelectionStep = () => {
	const { signatureData, updateSignatureData } = useEpiSignaturePage();
	const [epis, setEpis] = useState<IEpiItem[]>(MOCK_EPIS);
	const [signatoryCount, setSignatoryCount] = useState(signatureData.signatoryCount || 1);

	const selectedEpis = epis.filter(epi => epi.isSelected);
	const hasSelectedEpis = selectedEpis.length > 0;

	const handleEpiToggle = (epiId: number) => {
		setEpis(prev => prev.map(epi => 
			epi.id === epiId 
				? { ...epi, isSelected: !epi.isSelected }
				: epi
		));
	};

	const handleQuantityChange = (epiId: number, newQuantity: number) => {
		if (newQuantity < 1) return;
		setEpis(prev => prev.map(epi => 
			epi.id === epiId 
				? { ...epi, quantity: newQuantity }
				: epi
		));
	};

	const handleReturnToggle = (epiId: number) => {
		setEpis(prev => prev.map(epi => 
			epi.id === epiId 
				? { ...epi, needsReturn: !epi.needsReturn }
				: epi
		));
	};

	const handleSignatoryCountChange = (newCount: number) => {
		if (newCount < 1) return;
		setSignatoryCount(newCount);
		updateSignatureData({ 
			signatoryCount: newCount,
			selectedEpis: selectedEpis 
		});
	};

	const categories = Array.from(new Set(epis.map(epi => epi.category))).filter(Boolean);

	return (
		<div className="space-y-6">
			<div className="text-center mb-6">
				<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">
					Seleção de EPIs
				</h2>
				<p className="text-sm md:text-base text-gray-600">
					Escolha os EPIs que serão entregues e configure as quantidades
				</p>
			</div>

			{/* Contador de Signatários */}
			<Card className="mb-6">
				<CardHeader>
					<CardTitle className="flex items-center gap-2 text-lg">
						<Users className="w-5 h-5" />
						Quantidade de Signatários
					</CardTitle>
					<CardDescription>
						Quantas pessoas receberão os mesmos EPIs selecionados?
					</CardDescription>
				</CardHeader>
				<CardContent>
					<div className="flex items-center gap-4">
						<Button
							variant="outline"
							size="icon"
							onClick={() => handleSignatoryCountChange(signatoryCount - 1)}
							disabled={signatoryCount <= 1}
						>
							<Minus className="w-4 h-4" />
						</Button>
						<div className="flex-1 max-w-20">
							<Input
								type="number"
								value={signatoryCount}
								onChange={(e) => handleSignatoryCountChange(Number(e.target.value))}
								min="1"
								className="text-center"
							/>
						</div>
						<Button
							variant="outline"
							size="icon"
							onClick={() => handleSignatoryCountChange(signatoryCount + 1)}
						>
							<Plus className="w-4 h-4" />
						</Button>
						<span className="text-sm text-gray-600 ml-4">
							{signatoryCount === 1 ? "pessoa" : "pessoas"}
						</span>
					</div>
				</CardContent>
			</Card>

			{/* Lista de EPIs por Categoria */}
			<div className="space-y-6">
				{categories.map(category => (
					<div key={category}>
						<h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center gap-2">
							<Shield className="w-5 h-5" />
							{category}
						</h3>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							{epis.filter(epi => epi.category === category).map(epi => (
								<Card
									key={epi.id}
									className={cn(
										"cursor-pointer transition-all duration-200",
										"border-2",
										epi.isSelected
											? "border-primary bg-primary/5 shadow-md"
											: "border-gray-200 hover:border-gray-300 hover:shadow-sm"
									)}
									onClick={() => handleEpiToggle(epi.id)}
								>
									<CardHeader className="pb-3">
										<div className="flex items-start justify-between">
											<div className="flex-1">
												<CardTitle className="text-base mb-1">
													{epi.name}
												</CardTitle>
												{epi.description && (
													<CardDescription className="text-sm">
														{epi.description}
													</CardDescription>
												)}
											</div>
											{epi.isSelected && (
												<Check className="w-5 h-5 text-primary flex-shrink-0" />
											)}
										</div>
									</CardHeader>

									{epi.isSelected && (
										<>
											<Separator />
											<CardContent className="pt-4">
												<div className="space-y-4">
													{/* Quantidade */}
													<div>
														<Label className="text-sm font-medium mb-2 block">
															Quantidade por pessoa
														</Label>
														<div className="flex items-center gap-2">
															<Button
																variant="outline"
																size="sm"
																onClick={(e) => {
																	e.stopPropagation();
																	handleQuantityChange(epi.id, epi.quantity - 1);
																}}
																disabled={epi.quantity <= 1}
															>
																<Minus className="w-3 h-3" />
															</Button>
															<Input
																type="number"
																value={epi.quantity}
																onChange={(e) => {
																	e.stopPropagation();
																	handleQuantityChange(epi.id, Number(e.target.value));
																}}
																min="1"
																className="w-16 text-center"
																onClick={(e) => e.stopPropagation()}
															/>
															<Button
																variant="outline"
																size="sm"
																onClick={(e) => {
																	e.stopPropagation();
																	handleQuantityChange(epi.id, epi.quantity + 1);
																}}
															>
																<Plus className="w-3 h-3" />
															</Button>
														</div>
													</div>

													{/* Necessita Devolução */}
													<div className="flex items-center justify-between">
														<Label className="text-sm font-medium">
															Necessita devolução?
														</Label>
														<Button
															variant={epi.needsReturn ? "default" : "outline"}
															size="sm"
															onClick={(e) => {
																e.stopPropagation();
																handleReturnToggle(epi.id);
															}}
														>
															{epi.needsReturn ? "Sim" : "Não"}
														</Button>
													</div>
												</div>
											</CardContent>
										</>
									)}
								</Card>
							))}
						</div>
					</div>
				))}
			</div>

			{/* Resumo da Seleção */}
			{hasSelectedEpis && (
				<Card className="bg-blue-50 border-blue-200">
					<CardHeader>
						<CardTitle className="text-lg text-blue-900">
							Resumo da Seleção
						</CardTitle>
					</CardHeader>
					<CardContent>
						<div className="space-y-2">
							<p className="text-sm text-blue-800">
								<strong>{selectedEpis.length}</strong> EPIs selecionados para{" "}
								<strong>{signatoryCount}</strong> {signatoryCount === 1 ? "pessoa" : "pessoas"}
							</p>
							<div className="text-xs text-blue-700">
								{selectedEpis.map(epi => (
									<div key={epi.id} className="flex justify-between">
										<span>{epi.name}</span>
										<span>
											{epi.quantity}x {epi.needsReturn ? "(devolução)" : "(consumo)"}
										</span>
									</div>
								))}
							</div>
						</div>
					</CardContent>
				</Card>
			)}
		</div>
	);
};
