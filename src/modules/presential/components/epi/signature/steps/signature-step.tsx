import { useEpiSignaturePage } from "@/modules/presential/hooks/epi/use-epi-signature-page.hook";
import { Button } from "@/shared/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/components/ui/card";
import { cn } from "@/shared/lib/utils";
import { Check, FileText, PenTool, RotateCcw } from "lucide-react";
import { useCallback, useRef, useState } from "react";

export const SignatureStep = () => {
	const { signatureData } = useEpiSignaturePage();
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const [isDrawing, setIsDrawing] = useState(false);
	const [hasSignature, setHasSignature] = useState(false);
	const [lastPoint, setLastPoint] = useState<{ x: number; y: number } | null>(null);

	const startDrawing = useCallback((event: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
		const canvas = canvasRef.current;
		if (!canvas) return;

		const rect = canvas.getBoundingClientRect();
		const scaleX = canvas.width / rect.width;
		const scaleY = canvas.height / rect.height;

		let clientX: number, clientY: number;

		if ("touches" in event) {
			event.preventDefault();
			clientX = event.touches[0].clientX;
			clientY = event.touches[0].clientY;
		} else {
			clientX = event.clientX;
			clientY = event.clientY;
		}

		const x = (clientX - rect.left) * scaleX;
		const y = (clientY - rect.top) * scaleY;

		setIsDrawing(true);
		setLastPoint({ x, y });
		setHasSignature(true);
	}, []);

	const draw = useCallback(
		(event: React.MouseEvent<HTMLCanvasElement> | React.TouchEvent<HTMLCanvasElement>) => {
			if (!isDrawing || !lastPoint) return;

			const canvas = canvasRef.current;
			const ctx = canvas?.getContext("2d");
			if (!canvas || !ctx) return;

			const rect = canvas.getBoundingClientRect();
			const scaleX = canvas.width / rect.width;
			const scaleY = canvas.height / rect.height;

			let clientX: number, clientY: number;

			if ("touches" in event) {
				event.preventDefault();
				clientX = event.touches[0].clientX;
				clientY = event.touches[0].clientY;
			} else {
				clientX = event.clientX;
				clientY = event.clientY;
			}

			const x = (clientX - rect.left) * scaleX;
			const y = (clientY - rect.top) * scaleY;

			ctx.beginPath();
			ctx.moveTo(lastPoint.x, lastPoint.y);
			ctx.lineTo(x, y);
			ctx.strokeStyle = "#000";
			ctx.lineWidth = 2;
			ctx.lineCap = "round";
			ctx.lineJoin = "round";
			ctx.stroke();

			setLastPoint({ x, y });
		},
		[isDrawing, lastPoint]
	);

	const stopDrawing = useCallback(() => {
		setIsDrawing(false);
		setLastPoint(null);
	}, []);

	const clearSignature = useCallback(() => {
		const canvas = canvasRef.current;
		const ctx = canvas?.getContext("2d");
		if (!canvas || !ctx) return;

		ctx.clearRect(0, 0, canvas.width, canvas.height);
		setHasSignature(false);
	}, []);

	const getSignatureData = useCallback(() => {
		const canvas = canvasRef.current;
		if (!canvas || !hasSignature) return null;

		return canvas.toDataURL("image/png");
	}, [hasSignature]);

	// Mock do termo - substituir por dados reais
	const termContent = `
TERMO DE RESPONSABILIDADE E RECEBIMENTO DE EQUIPAMENTOS DE PROTEÇÃO INDIVIDUAL (EPI)

Eu, ${signatureData.personName || "[NOME]"}, portador(a) do CPF ${
		signatureData.personCpf || "[CPF]"
	}, declaro ter recebido os seguintes Equipamentos de Proteção Individual (EPI):

${signatureData.selectedEpis?.map(epi => `• ${epi.name} - Quantidade: ${epi.quantity}`).join("\n") || "• [EPIs selecionados]"}

Declaro estar ciente de que:
1. É obrigatório o uso dos EPIs durante a execução das atividades;
2. Sou responsável pela guarda e conservação dos equipamentos;
3. Em caso de dano ou perda, comunicarei imediatamente ao responsável;
4. Os EPIs devem ser devolvidos ao final das atividades, quando aplicável.

Estou ciente das normas de segurança e me comprometo a utilizá-los adequadamente.
	`.trim();

	return (
		<div className="space-y-6">
			<div className="text-center mb-6">
				<h2 className="text-xl md:text-2xl font-semibold text-gray-900 mb-2">Assinatura do Termo</h2>
				<p className="text-sm md:text-base text-gray-600">Leia o termo e assine para confirmar o recebimento dos EPIs</p>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Termo de Responsabilidade */}
				<Card className="h-fit">
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<FileText className="w-5 h-5" />
							Termo de Responsabilidade
						</CardTitle>
						<CardDescription>Leia atentamente antes de assinar</CardDescription>
					</CardHeader>
					<CardContent>
						<div className="bg-gray-50 p-4 rounded-lg max-h-96 overflow-y-auto">
							<pre className="text-sm text-gray-700 whitespace-pre-wrap font-sans leading-relaxed">{termContent}</pre>
						</div>
					</CardContent>
				</Card>

				{/* Área de Assinatura */}
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<PenTool className="w-5 h-5" />
							Assinatura Digital
						</CardTitle>
						<CardDescription>Assine no campo abaixo usando o mouse ou toque</CardDescription>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="relative">
							<canvas
								ref={canvasRef}
								width={400}
								height={200}
								className={cn(
									"w-full h-48 border-2 border-dashed rounded-lg cursor-crosshair",
									"touch-none select-none",
									hasSignature ? "border-green-300 bg-green-50" : "border-gray-300 bg-gray-50"
								)}
								onMouseDown={startDrawing}
								onMouseMove={draw}
								onMouseUp={stopDrawing}
								onMouseLeave={stopDrawing}
								onTouchStart={startDrawing}
								onTouchMove={draw}
								onTouchEnd={stopDrawing}
							/>
							{!hasSignature && (
								<div className="absolute inset-0 flex items-center justify-center pointer-events-none">
									<div className="text-center">
										<PenTool className="w-8 h-8 text-gray-400 mx-auto mb-2" />
										<p className="text-sm text-gray-500">Clique e arraste para assinar</p>
										<p className="text-xs text-gray-400 mt-1">Use o mouse ou toque na tela</p>
									</div>
								</div>
							)}
						</div>

						<div className="flex gap-2">
							<Button variant="outline" onClick={clearSignature} disabled={!hasSignature} className="flex-1">
								<RotateCcw className="w-4 h-4 mr-2" />
								Limpar
							</Button>
						</div>

						{hasSignature && (
							<div className="p-3 bg-green-50 border border-green-200 rounded-lg">
								<div className="flex items-center gap-2">
									<Check className="w-4 h-4 text-green-600" />
									<span className="text-sm font-medium text-green-800">Assinatura capturada com sucesso</span>
								</div>
							</div>
						)}
					</CardContent>
				</Card>
			</div>

			{/* Resumo dos Dados */}
			<Card className="bg-blue-50 border-blue-200">
				<CardHeader>
					<CardTitle className="text-lg text-blue-900">Resumo dos Dados</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
						<div>
							<p className="font-medium text-blue-800 mb-1">Dados Pessoais:</p>
							<p className="text-blue-700">Nome: {signatureData.personName || "Não informado"}</p>
							<p className="text-blue-700">CPF: {signatureData.personCpf || "Não informado"}</p>
						</div>
						<div>
							<p className="font-medium text-blue-800 mb-1">EPIs Selecionados:</p>
							{signatureData.selectedEpis?.length ? (
								signatureData.selectedEpis.map((epi, index) => (
									<p key={index} className="text-blue-700">
										• {epi.name} ({epi.quantity}x)
									</p>
								))
							) : (
								<p className="text-blue-700">Nenhum EPI selecionado</p>
							)}
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Instruções */}
			<Card className="border-yellow-200 bg-yellow-50">
				<CardContent className="pt-6">
					<div className="flex items-start gap-3">
						<div className="w-2 h-2 rounded-full bg-yellow-500 mt-2 flex-shrink-0" />
						<div className="text-sm text-yellow-800">
							<p className="font-medium mb-1">Instruções importantes:</p>
							<ul className="space-y-1 text-xs">
								<li>• Leia todo o termo antes de assinar</li>
								<li>• Certifique-se de que todos os dados estão corretos</li>
								<li>• A assinatura confirma o recebimento e responsabilidade pelos EPIs</li>
								<li>• Mantenha os EPIs em bom estado de conservação</li>
							</ul>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};
