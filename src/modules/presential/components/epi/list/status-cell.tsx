import { CheckCir<PERSON>, Clock } from "lucide-react";

interface StatusCellProps {
	isSigned: boolean;
}

export function StatusCell({ isSigned }: StatusCellProps) {
	if (isSigned) {
		return (
			<div className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-emerald-50 border border-emerald-200 text-emerald-700 text-xs font-medium">
				<CheckCircle size={12} className="text-emerald-600" />
				Assinado
			</div>
		);
	}

	return (
		<div className="inline-flex items-center gap-1.5 px-3 py-1.5 rounded-full bg-amber-50 border border-amber-200 text-amber-700 text-xs font-medium">
			<Clock size={12} className="text-amber-600" />
			Pendente
		</div>
	);
}
