"use client";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from "@tanstack/react-table";
import { Shield } from "lucide-react";
import { useMemo } from "react";

import { EpiReturnFiltersType } from "@/modules/presential/hooks/epi/use-epi-return-filters.hook";
import { useEpiReturnsList } from "@/modules/presential/hooks/epi/use-epi-returns-list.hook";
import { IEpiReturn } from "@/modules/presential/services/requests/epi";
import { Separator } from "@/shared/components/ui/separator";
import { UseFormReturn } from "react-hook-form";
import { DateCell } from "./date-cell";
import { MobileEpiReturnCard } from "./mobile-epi-return-card";
import { PersonCell } from "./person-cell";
import { StatusCell } from "./status-cell";
import { TermCell } from "./term-cell";

interface ISignatureListParams {
	form: UseFormReturn<EpiReturnFiltersType>;
}

const COLUMN_WIDTHS = [280, 220, 180, 140, 200];

export const SignatureReturnEpiList = (props: ISignatureListParams) => {
	const { form } = props;
	const watchPersonId = form.watch("personId");
	const watchSigned = form.watch("signed");

	const filterParams = {
		personId: watchPersonId ? Number(watchPersonId) : undefined,
		signed: watchSigned === "all" ? undefined : watchSigned === "true",
	};

	const { data, isLoading, getTermNameById, formatDate, isTermsLoading } = useEpiReturnsList({
		params: filterParams,
	});

	const columnWidths = useMemo(() => [280, 220, 180, 140, 200], []);

	const columns = useMemo<ColumnDef<IEpiReturn>[]>(
		() => [
			{
				header: "TERMO ASSOCIADO",
				accessorKey: "idTerm",
				cell: ({ getValue }) => <TermCell termId={getValue() as number} getTermName={getTermNameById} isLoading={isTermsLoading} />,
				size: columnWidths[0],
			},
			{
				header: "PESSOA",
				accessorKey: "idPerson",
				cell: ({ getValue }) => <PersonCell id={getValue() as number} />,
				size: columnWidths[1],
			},
			{
				header: "STATUS",
				accessorKey: "isSigned",
				cell: ({ getValue }) => <StatusCell isSigned={getValue() as boolean} />,
				size: columnWidths[2],
			},
			{
				header: "DATA DE ASSINATURA",
				accessorKey: "signatureDate",
				cell: ({ getValue }) => <DateCell dateString={getValue() as string} formatDate={formatDate} />,
				size: columnWidths[3],
			},
		],
		[getTermNameById, formatDate, isTermsLoading, columnWidths]
	);

	const table = useReactTable({
		data: Array.isArray(data?.data) ? data.data : [],
		columns,
		getCoreRowModel: getCoreRowModel(),
	});

	const renderTableContent = () => {
		if (isLoading) {
			return (
				<TableBody className="bg-white px-15">
					{[...Array(2)].map((_, idx) => (
						<TableRow key={idx}>
							{COLUMN_WIDTHS.map((width, colIdx) => (
								<TableCell key={colIdx} className="px-4 py-4" style={{ width }}>
									<Skeleton className="h-5" style={{ width: width - 32 }} />
								</TableCell>
							))}
						</TableRow>
					))}
				</TableBody>
			);
		}

		if (!data?.success) {
			return (
				<TableBody>
					<TableRow>
						<TableCell colSpan={7} className="h-32 text-center">
							<p className="text-gray-500">Erro ao carregar as devoluções de EPI</p>
							<p className="text-sm text-gray-500">{data?.data?.message}</p>
						</TableCell>
					</TableRow>
				</TableBody>
			);
		}

		if (data.data.length === 0) {
			return (
				<TableBody className="bg-white">
					<TableRow>
						<TableCell colSpan={5} className="h-32 text-center">
							<Shield className="mx-auto h-12 w-12 opacity-30" />
							<h3 className="mt-2 text-lg font-semibold text-gray-500">Nenhuma devolução de EPI encontrada</h3>
							<p className="mt-1 text-sm text-gray-500">Não há devoluções de EPI para os filtros selecionados</p>
						</TableCell>
					</TableRow>
				</TableBody>
			);
		}

		return (
			<TableBody className="bg-white px-15">
				{table.getRowModel().rows.map(row => (
					<TableRow key={row.id} className="hover:bg-gray-100/40 transition-colors">
						{row.getVisibleCells().map((cell, idx) => (
							<TableCell key={cell.id} className="px-4 py-4" style={{ width: COLUMN_WIDTHS[idx] }}>
								{flexRender(cell.column.columnDef.cell, cell.getContext())}
							</TableCell>
						))}
					</TableRow>
				))}
			</TableBody>
		);
	};

	const renderMobileContent = () => {
		if (isLoading) {
			return (
				<div className="space-y-4 px-4 py-2">
					{[...Array(3)].map((_, idx) => (
						<Skeleton key={idx} className="h-20 w-full rounded-lg" />
					))}
				</div>
			);
		}
		if (!data?.success) {
			return (
				<div className="text-center py-8 text-gray-500 px-4">
					<p>Erro ao carregar as devoluções de EPI</p>
					<p className="text-sm">{data?.data?.message}</p>
				</div>
			);
		}
		if (data.data.length === 0) {
			return (
				<div className="text-center py-8 bg-white text-gray-500 px-4">
					<Shield className="mx-auto h-12 w-12 opacity-30" />
					<h3 className="mt-2 text-lg font-semibold">Nenhuma devolução de EPI encontrada</h3>
					<p className="mt-1 text-sm">Não há devoluções de EPI para os filtros selecionados</p>
				</div>
			);
		}
		return (
			<div className="space-y-4 px-2 py-2">
				{data.data.map(epiReturn => (
					<MobileEpiReturnCard
						key={epiReturn.id}
						epiReturn={epiReturn}
						getTermName={getTermNameById}
						formatDate={formatDate}
						isTermsLoading={isTermsLoading}
					/>
				))}
			</div>
		);
	};

	return (
		<div className="flex flex-col max-w-full w-full shadow rounded-lg overflow-hidden">
			<header className="flex p-4 items-center gap-3">
				<Shield className="w-6 h-6 text-primary" />
				<h2 className="text-lg font-semibold text-gray-900">Lista de Devoluções de EPI</h2>
			</header>
			<Separator className="w-full" />
			<div className="lg:hidden">{renderMobileContent()}</div>
			<main className="bg-gray-100 overflow-x-auto hidden lg:block">
				<Table>
					<TableHeader className="px-15">
						{table.getHeaderGroups().map(headerGroup => (
							<TableRow key={headerGroup.id}>
								{headerGroup.headers.map((header, idx) => (
									<TableHead
										key={header.id}
										className="px-4 py-3 text-sm text-gray-500 font-normal text-left"
										style={{ width: COLUMN_WIDTHS[idx] }}
									>
										{flexRender(header.column.columnDef.header, header.getContext())}
									</TableHead>
								))}
							</TableRow>
						))}
					</TableHeader>
					{renderTableContent()}
				</Table>
			</main>
		</div>
	);
};
