import { EpiReturnFiltersType } from "@/modules/presential/hooks/epi/use-epi-return-filters.hook";
import { useFindAllPersonQuery } from "@/modules/presential/hooks/person/find-all-person-query.hook";
import { IPerson } from "@/modules/presential/services/requests/person/find-all";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { cn } from "@/shared/lib/utils";
import { Search, User, X } from "lucide-react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { UseFormReturn } from "react-hook-form";

interface PersonSearchInputProps {
	form: UseFormReturn<EpiReturnFiltersType>;
	handleReset?: () => void;
}

export const PersonSearchInput = ({ form }: PersonSearchInputProps) => {
	const [searchTerm, setSearchTerm] = useState<string | null>(null);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState<string | null>(null);
	const [isOpen, setIsOpen] = useState(false);
	const [selectedPerson, setSelectedPerson] = useState<IPerson | null>(null);

	useEffect(() => {
		const timer = setTimeout(() => {
			setDebouncedSearchTerm(searchTerm);
		}, 300);

		return () => clearTimeout(timer);
	}, [searchTerm]);

	const { data: personsData, isLoading } = useFindAllPersonQuery({
		name: debouncedSearchTerm?.trim() || undefined,
	});

	const persons = useMemo(() => {
		if (!personsData?.success) return [];
		return personsData.data || [];
	}, [personsData]);

	const handleSelectPerson = useCallback(
		(person: IPerson) => {
			setSelectedPerson(person);
			setSearchTerm(person.name);
			setIsOpen(false);
			form.setValue("personId", String(person.id));
		},
		[form]
	);

	const handleClearSelection = useCallback(() => {
		setSelectedPerson(null);
		setSearchTerm("");
		setIsOpen(false);
		form.setValue("personId", "");
	}, [form]);

	const handleInputChange = useCallback(
		(e: React.ChangeEvent<HTMLInputElement>) => {
			const value = e.target.value;
			setSearchTerm(value);
			setIsOpen(value.length > 0);

			if (!value) {
				handleClearSelection();
			} else {
				if (selectedPerson && value !== selectedPerson.name) {
					setSelectedPerson(null);
					form.setValue("personId", "");
				}
			}
		},
		[selectedPerson, form, handleClearSelection]
	);

	const handleInputFocus = useCallback(() => {
		setIsOpen(true);
	}, []);

	const handleInputBlur = useCallback(() => {
		setTimeout(() => {
			setIsOpen(false);
		}, 200);
	}, []);

	return (
		<div className="space-y-2 group md:col-span-2 relative">
			<Label className="flex items-center gap-2 text-xs font-medium text-gray-600">
				<User className="w-3.5 h-3.5 text-gray-500" />
				Pessoa
			</Label>
			<div className="relative">
				<Input
					value={searchTerm ?? ""}
					onChange={handleInputChange}
					onFocus={handleInputFocus}
					onBlur={handleInputBlur}
					placeholder="Digite o nome da pessoa"
					className="h-9 text-sm pr-16 transition-all border-gray-200 focus:border-pormade focus:ring focus:ring-pormade/10"
				/>
				<div className="absolute right-2 top-2 flex items-center gap-1">
					{selectedPerson && (
						<button type="button" onClick={handleClearSelection} className="p-0.5 rounded hover:bg-gray-100 transition-colors">
							<X className="w-3 h-3 text-gray-400 hover:text-gray-600" />
						</button>
					)}
					<Search className="w-4 h-4 text-gray-400 group-hover:text-pormade transition-colors" />
				</div>

				{isOpen && (
					<div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto">
						{isLoading ? (
							<div className="p-2 space-y-2 flex flex-col items-center">
								<Skeleton className="h-8 w-full" />
								<Skeleton className="h-8 w-full" />
								<Skeleton className="h-8 w-full" />
								<Search className="w-6 h-6 text-gray-400 animate-spin" />
							</div>
						) : persons.length > 0 ? (
							<div className="py-1">
								{persons.map(person => (
									<button
										key={person.id}
										type="button"
										onClick={() => handleSelectPerson(person)}
										className={cn(
											"w-full px-3 py-2 text-left hover:bg-gray-50 transition-colors",
											"flex items-center justify-between group/item"
										)}
									>
										<div className="flex flex-col">
											<span className="text-sm font-medium text-gray-900">{person.name}</span>
											<span className="text-xs text-gray-500">CPF: {person.cpf}</span>
										</div>
										{/* <span className="text-xs text-gray-400 group-hover/item:text-gray-600">ID: {person.id}</span> */}
									</button>
								))}
							</div>
						) : persons.length <= 0 ? (
							<div className="p-3 text-center text-gray-500 text-sm flex flex-col items-center">
								<Search className="w-6 h-6 text-gray-400" />
								Nenhuma pessoa encontrada
							</div>
						) : null}
					</div>
				)}
			</div>
		</div>
	);
};
