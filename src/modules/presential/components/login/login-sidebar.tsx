import { SigningIllustration } from "@/modules/presential/components/login/signing-illustration";
import LogoAssinaturaBranca from "@/shared/assets/logos/logo-admin-branca.png";
import Image from "next/image";
import { FiUser } from "react-icons/fi";

export const LoginSidebar = () => {
	return (
		<div className="hidden md:flex md:w-1/2 relative bg-gradient-to-br from-gray-900 to-gray-800">
			<div className="absolute inset-0 bg-black/20" />
			<div className="relative w-full z-10 flex flex-col justify-center items-center p-16">
				<div className="max-w-lg flex flex-col items-center text-center space-y-12">
					<div className="space-y-6">
						<Image src={LogoAssinaturaBranca} alt="Logo da empresa" width={200} className="mx-auto drop-shadow" />
						<p className="text-xl text-gray-300 leading-relaxed max-w-md mx-auto">
							Gerencie assinaturas presenciais de forma segura e eficiente em nossa plataforma.
						</p>
					</div>

					<div className="w-[420px] h-[420px] relative">
						<SigningIllustration />
					</div>

					<div className="flex items-center space-x-8 text-gray-400">
						<div className="flex items-center space-x-2">
							<div className="w-10 h-10 rounded-full bg-gray-700/50 flex items-center justify-center">
								<FiUser className="w-5 h-5" />
							</div>
							<span>Acesso Seguro</span>
						</div>
						<div className="w-px h-6 bg-gray-700" />
						<div className="flex items-center space-x-2">
							<div className="w-10 h-10 rounded-full bg-gray-700/50 flex items-center justify-center">
								<svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
									/>
								</svg>
							</div>
							<span>Certificado Digital</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};
