import { useCreateTermMutation } from "@/modules/presential/hooks/terms/create-term-mutation.hook";
import { useUpdateTermMutation } from "@/modules/presential/hooks/terms/update-term-mutation.hook";
import { createNewTermFormSchema, editTermFormSchema } from "@/modules/presential/validators/create-term.form";
import { Modal } from "@/shared/components/custom/modal";
import { Button } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { Check, FilePlus, Pencil, Plus, X } from "lucide-react";
import { useEffect } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { TermForm } from "../forms/term-form";

interface TermModalProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
	mode: "create" | "edit";
	initialData?: {
		title: string;
		idTermTag: string;
	};
	selectedTermId?: number | null;
}

export const TermModal = ({
	isOpen,
	onOpenChange,
	onClose,
	mode,
	initialData = { title: "", idTermTag: "" },
	selectedTermId = null,
}: TermModalProps) => {
	const createTermMutation = useCreateTermMutation();
	const updateTermMutation = useUpdateTermMutation();

	//TODO: criar modo para criar termo

	const methods = useForm<{
		title: string;
		idTermTag: string;
		termo?: File;
	}>({
		resolver: zodResolver(mode === "create" ? createNewTermFormSchema : editTermFormSchema),
		defaultValues: {
			...initialData,
			termo: undefined,
		},
		mode: "onChange",
	});

	const { handleSubmit, formState, reset } = methods;

	useEffect(() => {
		reset(initialData);
	}, [isOpen, reset, initialData]);

	const onSubmit = (data: { title: string; idTermTag: string; termo?: File }) => {
		if (mode === "create") {
			createTermMutation.mutate(
				{
					title: data.title,
					idTermTag: Number(data.idTermTag),
					termo: data.termo as File,
				},
				{
					onSuccess: () => {
						reset({ title: "", idTermTag: "" });
						onOpenChange(false);
						onClose();
					},
				}
			);
		} else if (mode === "edit" && selectedTermId) {
			updateTermMutation.mutate(
				{
					id: selectedTermId,
					body: {
						title: data.title,
						idTermTag: Number(data.idTermTag),
					},
				},
				{
					onSuccess: () => {
						reset({ title: "", idTermTag: "" });
						onOpenChange(false);
						onClose();
					},
				}
			);
		}
	};

	const isPending = mode === "create" ? createTermMutation.isPending : updateTermMutation.isPending;

	return (
		<Modal
			isOpen={isOpen}
			shouldCloseOnOverlayClick
			onRequestClose={() => {
				onOpenChange(false);
				onClose();
			}}
		>
			<FormProvider {...methods}>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="p-6 w-[90vw] max-w-md bg-white rounded-lg shadow-lg">
						<header className="flex items-center gap-3 mb-2">
							<div className="p-2 bg-gray-600/15 rounded-lg">
								{mode === "create" ? <FilePlus className="h-6 w-6 text-gray-800" /> : <Pencil className="h-6 w-6 text-gray-800" />}
							</div>
							<h2 id="modal-title" className="text-xl font-semibold text-gray-800">
								{mode === "create" ? "Criar Termo" : "Editar Termo"}
							</h2>
						</header>
						<Separator className="my-4" />
						<TermForm control={methods.control} errors={formState.errors} mode={mode} />
						<div className="flex justify-end gap-3 mt-6">
							<Button
								type="button"
								variant="outline"
								onClick={() => {
									onOpenChange(false);
									onClose();
								}}
								className="flex items-center gap-2 rounded-lg border-gray-300 hover:bg-gray-100 hover:text-gray-900 transition-colors"
							>
								<X className="h-4 w-4" />
								Cancelar
							</Button>
							<Button
								type="submit"
								disabled={isPending || formState.isSubmitting}
								className="flex rounded-lg bg-gradient-to-br from-gray-800 to-gray-600 items-center gap-2"
							>
								{isPending || formState.isSubmitting ? (
									<>
										<div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full"></div>
										<span className="ml-1">{mode === "create" ? "Criando..." : "Atualizando..."}</span>
									</>
								) : (
									<>
										{mode === "create" ? <Plus className="h-4 w-4" /> : <Check className="h-4 w-4" />}
										<span>{mode === "create" ? "Criar Termo" : "Atualizar Termo"}</span>
									</>
								)}
							</Button>
						</div>
					</div>
				</form>
			</FormProvider>
		</Modal>
	);
};
