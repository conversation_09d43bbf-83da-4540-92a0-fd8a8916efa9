import { TermModal } from "./term-modal";

interface CreateTermModalProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export const CreateTermModal = ({ isOpen, onOpenChange, onClose }: CreateTermModalProps) => {
	return (
		<TermModal
			isOpen={isOpen}
			onOpenChange={onOpenChange}
			mode="create"
			initialData={{ title: "", idTermTag: "" }}
			selectedTermId={null}
			onClose={onClose}
		/>
	);
};
