import { useCreateTagMutation } from "@/modules/presential/hooks/terms/create-tag-mutation.hook";
import { createTagFormSchema } from "@/modules/presential/validators/create-tag.form";
import { Modal } from "@/shared/components/custom/modal";
import { But<PERSON> } from "@/shared/components/ui/button";
import { Separator } from "@/shared/components/ui/separator";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus, Tag, X } from "lucide-react";
import { FormProvider, useForm } from "react-hook-form";
import { TagForm } from "../forms/tag-form";

interface CreateTagModalProps {
	isOpen: boolean;
	onOpenChange: (open: boolean) => void;
	onClose: () => void;
}

export const CreateTagModal = ({ isOpen, onOpenChange, onClose }: CreateTagModalProps) => {
	const createTagMutation = useCreateTagMutation();

	const methods = useForm<{ name: string }>({
		resolver: zodResolver(createTagFormSchema),
		defaultValues: { name: "" },
		mode: "onChange",
	});

	const { handleSubmit, formState, reset } = methods;

	const onSubmit = (data: { name: string }) => {
		createTagMutation.mutate(
			{ name: data.name },
			{
				onSuccess: () => {
					reset();
					onOpenChange(false);
					onClose();
				},
			}
		);
	};

	return (
		<Modal
			isOpen={isOpen}
			shouldCloseOnOverlayClick
			onRequestClose={() => {
				onOpenChange(false);
				onClose();
			}}
		>
			<FormProvider {...methods}>
				<form onSubmit={handleSubmit(onSubmit)}>
					<div className="p-6 w-[90vw] max-w-md bg-white rounded-lg shadow-lg">
						<header className="flex items-center gap-3 mb-2">
							<div className="p-2 bg-gray-600/15 rounded-lg">
								<Tag className="h-6 w-6 text-gray-800" />
							</div>
							<h2 id="modal-title" className="text-xl font-semibold text-gray-800">
								Criar Tag
							</h2>
						</header>
						<Separator className="my-4" />
						<TagForm control={methods.control} errors={formState.errors} />
						<div className="flex justify-end gap-2 mt-6">
							<Button
								type="button"
								variant="outline"
								onClick={() => {
									onOpenChange(false);
									onClose();
								}}
								className="flex items-center gap-2 rounded-lg border-gray-300 hover:bg-gray-100 hover:text-gray-900 transition-colors"
							>
								<X className="h-4 w-4" />
								Cancelar
							</Button>
							<Button
								type="submit"
								className="flex rounded-lg bg-gradient-to-br from-gray-800 to-gray-600 items-center gap-2"
								disabled={!formState.isValid || createTagMutation.isPending}
							>
								<Plus className="h-4 w-4" />
								{createTagMutation.isPending ? "Criando..." : "Criar Tag"}
							</Button>
						</div>
					</div>
				</form>
			</FormProvider>
		</Modal>
	);
};
