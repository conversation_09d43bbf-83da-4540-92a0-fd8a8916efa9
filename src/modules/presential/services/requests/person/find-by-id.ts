"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { PERSON_ENDPOINTS } from "../../endpoints";
import { <PERSON>erson } from "./find-all";

export const findPersonByIdRequest = async (id: number): Promise<ApiResponseReturn<IPerson>> => {
	return createRequestAdmin({
		method: "GET",
		path: PERSON_ENDPOINTS.FIND_BY_ID(id),
	});
};
