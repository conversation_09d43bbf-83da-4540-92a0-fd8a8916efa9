"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { PERSON_ENDPOINTS } from "../../endpoints";

export interface IFindAllPersonParams {
	name?: string;
	cpf?: string;
}

export interface IPerson {
	id: number;
	name: string;
	cpf: string;
	sankhyaUser: string;
}

export const findAllPersonRequest = async (params: IFindAllPersonParams): Promise<ApiResponseReturn<IPerson[]>> => {
	console.log("params", params);

	return createRequestAdmin({
		method: "GET",
		path: PERSON_ENDPOINTS.FIND_ALL(params),
	});
};
