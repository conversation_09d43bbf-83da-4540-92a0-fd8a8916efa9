"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { EPI_ENDPOINTS } from "../../endpoints";

export interface IEpi {
	id: number;
	name: string;
	description?: string;
	code?: string;
}

export const findAllEpiRequest = async (): Promise<ApiResponseReturn<IEpi[]>> => {
	return createRequestAdmin({
		method: "GET",
		path: EPI_ENDPOINTS.FIND_ALL,
	});
};
