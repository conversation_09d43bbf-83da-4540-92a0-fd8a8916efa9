"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { EPI_ENDPOINTS } from "../../endpoints";

export interface ISignWithDrawalParams {
	idTerm: number;
	groupEpi: object[];
	personCpf: string;
	personName: string;
	photo: File;
}

export interface ISignEpiReturn {
	id: number;
	signatureDate: string;
	message: string;
}

export const signEpiWithDrawalRequest = async (params: ISignWithDrawalParams): Promise<ApiResponseReturn<ISignEpiReturn>> => {
	const formData = new FormData();
	formData.append("idTerm", params.idTerm.toString());
	formData.append("groupEpi", JSON.stringify(params.groupEpi));
	formData.append("personCpf", params.personCpf);
	formData.append("personName", params.personName);
	formData.append("photo", params.photo);

	return createRequestAdmin({
		method: "POST",
		path: EPI_ENDPOINTS.SIGN_EPI_WITHDRAWAL,
		body: formData,
	});
};
