"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { EPI_ENDPOINTS } from "../../endpoints";

export interface IEpiGroup {
	id: number;
	name: string;
	epis: {
		id: number;
		name: string;
		description?: string;
		code?: string;
	}[];
}

export const findEpiByGroupRequest = async (idWithdrawalEpiGroup: number): Promise<ApiResponseReturn<IEpiGroup>> => {
	return createRequestAdmin({
		method: "GET",
		path: EPI_ENDPOINTS.GET_BY_GROUP(idWithdrawalEpiGroup),
	});
};
