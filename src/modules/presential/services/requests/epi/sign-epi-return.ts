"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { EPI_ENDPOINTS } from "../../endpoints";
import { ISignEpiReturn } from "./sign-epi-withdrawal";

export interface ISignEpiParams {
	idTerm: number;
	idSignatureEpiGiveBack: number;
	personCpf: string;
	photo: File;
}

export const signEpiReturnRequest = async (params: ISignEpiParams): Promise<ApiResponseReturn<ISignEpiReturn>> => {
	const formData = new FormData();
	formData.append("idTerm", params.idTerm.toString());
	formData.append("idSignatureEpiGiveBack", params.idSignatureEpiGiveBack.toString());
	formData.append("personCpf", params.personCpf);
	formData.append("photo", params.photo);

	return createRequestAdmin({
		method: "POST",
		path: EPI_ENDPOINTS.SIGN_EPI_RETURN,
		body: formData,
	});
};
