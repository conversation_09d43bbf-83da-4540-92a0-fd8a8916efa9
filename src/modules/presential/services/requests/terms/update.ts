"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";
import { ITerm } from "./find-all";

export interface IUpdateTermBody {
	idTermTag: number;
	title: string;
}

export const updateTerm = async (id: string | number, body: IUpdateTermBody): Promise<ApiResponseReturn<ITerm>> => {
	return createRequestAdmin({
		method: "PUT",
		path: TERM_ENDPOINTS.UPDATE(id),
		body,
	});
};
