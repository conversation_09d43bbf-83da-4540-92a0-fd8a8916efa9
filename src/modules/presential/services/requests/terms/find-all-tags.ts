"use server";

import { createRequestAdmin } from "@/shared/lib/requests/create-request.lib";
import { ApiResponseReturn } from "@/shared/types/requests";
import { TERM_ENDPOINTS } from "../../endpoints";

export interface ITermTag {
	id: number;
	name: string;
}

export const findAllTermTags = async (): Promise<ApiResponseReturn<ITermTag[]>> => {
	return createRequestAdmin({
		method: "GET",
		path: TERM_ENDPOINTS.GET_ALL_TAGS,
	});
};
