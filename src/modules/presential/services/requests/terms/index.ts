import { api } from "@/shared/services/api";

export interface ITerm {
	id: number;
	title: string;
	idTermTag: number;
	termTag: {
		id: number;
		name: string;
	};
}

export interface ICreateTermBody {
	title: string;
	idTermTag: number;
}

export interface IUpdateTermBody {
	title: string;
	idTermTag: number;
}

export const termsService = {
	findAll: () => api.get<{ success: boolean; data: ITerm[] }>("/terms"),
	create: (body: ICreateTermBody) => api.post<{ success: boolean; data: ITerm }>("/terms", body),
	update: (id: number, body: IUpdateTermBody) => api.put<{ success: boolean; data: ITerm }>(`/terms/${id}`, body),
	delete: (id: number) => api.delete<{ success: boolean; data: null }>(`/terms/${id}`),
};
