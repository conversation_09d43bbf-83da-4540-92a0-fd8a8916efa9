import { FindAllEpiReturnParams } from "../requests/epi/find-all-return";
import { IFindAllPersonParams } from "../requests/person/find-all";

export const ADMIN_AUTH_ENDPOINTS = {
	LOGIN: "/admin/login",
} as const;

export const TERM_ENDPOINTS = {
	CREATE: "/termo",
	GET_ALL: "/termo/all",
	GET_BY_ID: (idTermo: string | number) => `/termo/${idTermo}`,
	UPDATE: (idTermo: string | number) => `/termo/${idTermo}`,
	CREATE_TAG: "/termo/tag",
	GET_ALL_TAGS: "/termo/tag/all",
	GET_TAG_BY_ID: (idTermoTag: string | number) => `/termo/tag/${idTermoTag}`,
} as const;

export const EPI_ENDPOINTS = {
	FIND_ALL: "/epi/all",
	GET_BY_GROUP: (idWithdrawalEpiGroup: number) => `/epi/group/${idWithdrawalEpiGroup}`,
	SIGN_EPI_WITHDRAWAL: "/assinatura/epi/assinar",
	GET_ALL_EPI_RETURN: (params: FindAllEpiReturnParams) => {
		const query = [
			params.personId !== undefined ? `idPerson=${params.personId}` : null,
			params.signed !== undefined ? `signed=${params.signed}` : null,
		]
			.filter(Boolean)
			.join("&");
		return `/assinatura/epi/devolucao/all${query ? `?${query}` : ""}`;
	},
	SIGN_EPI_RETURN: "/assinatura/epi/devolucao/assinar",
} as const;

export const PERSON_ENDPOINTS = {
	FIND_ALL: (params: IFindAllPersonParams) => {
		const query = [params.name ? `name=${encodeURIComponent(params.name)}` : null, params.cpf ? `cpf=${encodeURIComponent(params.cpf)}` : null]
			.filter(Boolean)
			.join("&");
		return `/pessoa/all${query ? `?${query}` : ""}`;
	},
	FIND_BY_ID: (idPerson: number) => `/pessoa/${idPerson}`,
} as const;
