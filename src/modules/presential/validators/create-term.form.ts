import { z } from "zod";

export const termFileSchema = z.instanceof(File).refine(file => file.type === "application/pdf", {
	message: "O arquivo deve ser um PDF",
});

export const createTermFormSchema = z.object({
	title: z.string().min(1, { message: "O título do termo é obrigatório" }),
	idTermTag: z.string().min(1, { message: "A tag do termo é obrigatória" }),
});

export const createNewTermFormSchema = createTermFormSchema.extend({
	termo: termFileSchema,
});

export const editTermFormSchema = createTermFormSchema;
