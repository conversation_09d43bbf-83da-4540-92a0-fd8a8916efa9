import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { loginRequest } from "../../services/requests/auth/login";
import { IAdminLoginFormSchema } from "../../validators/admin-login.form";

export const useLoginMutation = () => {
	const router = useRouter();

	const mutation = useMutation({
		mutationKey: ["login"],
		mutationFn: async ({ username, password, signIn }: IAdminLoginFormSchema) => {
			const res = await loginRequest({ user: username, senha: password }, !!signIn);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => router.replace("/admin"),
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	return {
		login: mutation.mutateAsync,
		isLoading: mutation.isPending,
	};
};
