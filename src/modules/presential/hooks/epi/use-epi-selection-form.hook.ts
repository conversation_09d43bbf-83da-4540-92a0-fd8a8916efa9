import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useFindAllPersonQuery } from "../person/find-all-person-query.hook";
import { useFindAllTermsQuery } from "../terms/find-all-terms-query.hook";
import { useEpiSignaturePage } from "./use-epi-signature-page.hook";

// Tipos temporários até implementar os hooks corretos
interface IPerson {
	id: number;
	name: string;
	cpf: string;
}

interface ITerm {
	id: number;
	fileName: string;
}

interface IEpiGroup {
	id: number;
	epis?: any[];
}

interface IEpiItem {
	id: number;
	name: string;
	quantity: number;
}

export const epiSelectionSchema = z.object({
	personId: z.string().min(1, "Selecione uma pessoa"),
	termId: z.string().min(1, "Selecione um termo"),
	epiGroupId: z.string().min(1, "Selecione um grupo de EPIs"),
});

export type EpiSelectionFormType = z.infer<typeof epiSelectionSchema>;

export interface IHandleEpiSelectionFormHook {
	form: ReturnType<typeof useForm<EpiSelectionFormType>>;
	personsData: IPerson[] | undefined;
	termsData: ITerm[] | undefined;
	epiGroupsData: IEpiGroup[] | undefined;
	epiItemsData: IEpiItem[] | undefined;
	isLoadingPersons: boolean;
	isLoadingTerms: boolean;
	isLoadingEpiGroups: boolean;
	isLoadingEpiItems: boolean;
	selectedPersonId: string | undefined;
	selectedTermId: string | undefined;
	selectedEpiGroupId: string | undefined;
	handlePersonChange: (personId: string) => void;
	handleTermChange: (termId: string) => void;
	handleEpiGroupChange: (epiGroupId: string) => void;
	onSubmit: (data: EpiSelectionFormType) => void;
}

export const useEpiSelectionForm = (): IHandleEpiSelectionFormHook => {
	const { updateSignatureData } = useEpiSignaturePage();

	const form = useForm<EpiSelectionFormType>({
		resolver: zodResolver(epiSelectionSchema),
		defaultValues: {
			personId: "",
			termId: "",
			epiGroupId: "",
		},
	});

	const selectedPersonId = form.watch("personId");
	const selectedTermId = form.watch("termId");
	const selectedEpiGroupId = form.watch("epiGroupId");

	// Queries - usando hooks existentes ou dados mock temporários
	const { data: personsData, isLoading: isLoadingPersons } = useFindAllPersonQuery();
	const { data: termsData, isLoading: isLoadingTerms } = useFindAllTermsQuery();

	// Mock data temporário para EPIs até implementar os hooks corretos
	const epiGroupsData: IEpiGroup[] = [
		{
			id: 1,
			epis: [
				{ id: 1, name: "Capacete", quantity: 1 },
				{ id: 2, name: "Luvas", quantity: 2 },
			],
		},
		{
			id: 2,
			epis: [
				{ id: 3, name: "Óculos", quantity: 1 },
				{ id: 4, name: "Botas", quantity: 1 },
			],
		},
	];
	const isLoadingEpiGroups = false;

	const epiItemsData: IEpiItem[] = selectedEpiGroupId ? epiGroupsData.find(g => g.id === Number(selectedEpiGroupId))?.epis || [] : [];
	const isLoadingEpiItems = false;

	const handlePersonChange = (personId: string) => {
		const selectedPerson = personsData?.find(p => p.id.toString() === personId);
		if (selectedPerson) {
			updateSignatureData({
				selectedPersonId: selectedPerson.id,
				personName: selectedPerson.name,
				personCpf: selectedPerson.cpf,
			});
		}
	};

	const handleTermChange = (termId: string) => {
		updateSignatureData({
			selectedTermId: Number(termId),
		});
	};

	const handleEpiGroupChange = (epiGroupId: string) => {
		updateSignatureData({
			selectedEpiGroupId: Number(epiGroupId),
		});
	};

	const onSubmit = (data: EpiSelectionFormType) => {
		// Atualizar dados finais antes de prosseguir
		const selectedPerson = personsData?.find(p => p.id.toString() === data.personId);
		const epiItems = epiItemsData || [];

		updateSignatureData({
			selectedPersonId: Number(data.personId),
			selectedTermId: Number(data.termId),
			selectedEpiGroupId: Number(data.epiGroupId),
			personName: selectedPerson?.name,
			personCpf: selectedPerson?.cpf,
			epiItems,
		});
	};

	return {
		form,
		personsData,
		termsData,
		epiGroupsData,
		epiItemsData,
		isLoadingPersons,
		isLoadingTerms,
		isLoadingEpiGroups,
		isLoadingEpiItems,
		selectedPersonId,
		selectedTermId,
		selectedEpiGroupId,
		handlePersonChange,
		handleTermChange,
		handleEpiGroupChange,
		onSubmit,
	};
};
