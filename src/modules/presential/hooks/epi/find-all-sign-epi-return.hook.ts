import { useQuery } from "@tanstack/react-query";
import { FindAllEpiReturnParams, findAllSignEpiReturnRequest } from "../../services/requests/epi/find-all-return";

export const useFindAllEpiReturnQuery = (params: FindAllEpiReturnParams, enabled: boolean = true) => {
	return useQuery({
		queryKey: ["epi", "return", "all", params],
		queryFn: () => findAllSignEpiReturnRequest(params),
		enabled,
	});
};
