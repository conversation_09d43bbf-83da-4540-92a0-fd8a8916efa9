import {
	can<PERSON>roceed<PERSON><PERSON>,
	currentStep<PERSON><PERSON>,
	isFirstStep<PERSON>tom,
	isLastStep<PERSON>tom,
	isLoading<PERSON><PERSON>,
	nextStep<PERSON>tom,
	previousStep<PERSON>tom,
	progressAtom,
	resetProcess<PERSON>tom,
	setStepAtom,
	totalStepsAtom,
} from "@/modules/presential/atoms/epi-signature.atoms";
import { useAtomValue, useSet<PERSON>tom } from "jotai";
import { Check, FileText, Shield, User } from "lucide-react";
import { useCallback } from "react";

export interface IStepConfig {
	id: number;
	title: string;
	description: string;
	icon: React.ElementType;
	mobileTitle?: string;
	isOptional?: boolean;
}

const STEP_CONFIG: IStepConfig[] = [
	{ id: 1, title: "Termo", description: "Seleção de Termo", mobileTitle: "Termo", icon: FileText },
	{ id: 2, title: "EPIs", description: "Seleção de EPIs e quantidade", mobileTitle: "EPIs", icon: Shield },
	{ id: 3, title: "Dad<PERSON>", description: "Preenchimento de dados pessoais", mobileTitle: "Dad<PERSON>", icon: User },
	{ id: 4, title: "Assinatura", description: "Leitura do termo e assinatura", mobileTitle: "Assinar", icon: FileText },
	{ id: 5, title: "Confirmação", description: "Confirmação de dados", mobileTitle: "Confirmar", icon: Check },
];

export interface IHandleEpiSignatureStepperHook {
	currentStep: number;
	totalSteps: number;
	isLoading: boolean;
	canProceed: boolean;
	canGoBack: boolean;
	isFirstStep: boolean;
	isLastStep: boolean;
	progress: number;
	stepConfig: IStepConfig[];
	handleNextStep: () => void;
	handlePreviousStep: () => void;
	handleStepChange: (step: number) => void;
	handleReset: () => void;
	getStepStatus: (stepId: number) => "pending" | "active" | "completed";
	getCurrentStepConfig: () => IStepConfig | undefined;
}

export const useEpiSignatureStepper = (): IHandleEpiSignatureStepperHook => {
	const currentStep = useAtomValue(currentStepAtom);
	const totalSteps = useAtomValue(totalStepsAtom);
	const isLoading = useAtomValue(isLoadingAtom);
	const canProceed = useAtomValue(canProceedAtom);
	const isFirstStep = useAtomValue(isFirstStepAtom);
	const isLastStep = useAtomValue(isLastStepAtom);
	const progress = useAtomValue(progressAtom);

	const nextStep = useSetAtom(nextStepAtom);
	const previousStep = useSetAtom(previousStepAtom);
	const setStep = useSetAtom(setStepAtom);
	const reset = useSetAtom(resetProcessAtom);

	const canGoBack = !isFirstStep && !isLoading;

	const handleNextStep = useCallback(() => {
		if (canProceed && !isLastStep) nextStep();
	}, [canProceed, isLastStep, nextStep]);

	const handlePreviousStep = useCallback(() => {
		if (canGoBack) previousStep();
	}, [canGoBack, previousStep]);

	const handleStepChange = useCallback(
		(step: number) => {
			if (step >= 1 && step <= totalSteps && !isLoading) setStep(step);
		},
		[totalSteps, isLoading, setStep]
	);

	const handleReset = useCallback(() => {
		if (!isLoading) reset();
	}, [isLoading, reset]);

	const getStepStatus = useCallback(
		(stepId: number): "pending" | "active" | "completed" => (stepId < currentStep ? "completed" : stepId === currentStep ? "active" : "pending"),
		[currentStep]
	);

	const getCurrentStepConfig = useCallback(() => STEP_CONFIG.find(step => step.id === currentStep), [currentStep]);

	return {
		currentStep,
		totalSteps,
		isLoading,
		canProceed,
		canGoBack,
		isFirstStep,
		isLastStep,
		progress,
		stepConfig: STEP_CONFIG,
		handleNextStep,
		handlePreviousStep,
		handleStepChange,
		handleReset,
		getStepStatus,
		getCurrentStepConfig,
	};
};
