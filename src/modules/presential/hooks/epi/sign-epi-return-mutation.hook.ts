import { useMutation, useQueryClient } from "@tanstack/react-query";
import toast from "react-hot-toast";
import { signEpiReturnRequest, ISignEpiParams } from "../../services/requests/epi/sign-epi-return";

export const useSignEpiReturnMutation = () => {
	const queryClient = useQueryClient();

	return useMutation({
		mutationKey: ["epi", "return", "sign"],
		mutationFn: async (params: ISignEpiParams) => {
			const res = await signEpiReturnRequest(params);
			if (!res.success) throw new Error(res.data.message);
			return res.data;
		},
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: ["epi", "return", "all"] });
			toast.dismiss();
			toast.success("Devolução de EPI assinada com sucesso!");
		},
		onError: (error: Error) => {
			toast.dismiss();
			toast.error(error.message);
		},
	});
};
