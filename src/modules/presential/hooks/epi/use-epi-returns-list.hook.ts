import { useFindAllEpiReturnQuery } from "@/modules/presential/hooks/epi/find-all-sign-epi-return.hook";
import { useFindAllTermsQuery } from "@/modules/presential/hooks/terms/find-all-terms-query.hook";
import { FindAllEpiReturnParams } from "@/modules/presential/services/requests/epi/find-all-return";
import { format } from "date-fns";
import { pt } from "date-fns/locale";
import { useCallback } from "react";

interface UseEpiReturnsListProps {
	params: FindAllEpiReturnParams;
}

export const useEpiReturnsList = ({ params }: UseEpiReturnsListProps) => {
	const { data, isLoading } = useFindAllEpiReturnQuery(params);
	const { data: termsData, isLoading: isTermsLoading } = useFindAllTermsQuery();

	const getTermNameById = useCallback(
		(id: number) => {
			if (!termsData?.success) return "Termo não encontrado";
			const term = termsData.data.find(term => term.id === id);
			return term?.title || "Termo não encontrado";
		},
		[termsData]
	);

	const formatDate = useCallback((dateString: string) => {
		try {
			return format(new Date(dateString), "dd/MM/yyyy 'às' HH:mm", { locale: pt });
		} catch {
			return "Data inválida";
		}
	}, []);

	return {
		data,
		isLoading,
		getTermNameById,
		formatDate,
		isTermsLoading,
	};
};
