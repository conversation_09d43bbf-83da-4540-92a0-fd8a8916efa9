import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useEpiSignaturePage } from "./use-epi-signature-page.hook";

export const epiSignatureSchema = z.object({
	personName: z.string().min(1, "Nome é obrigatório"),
	personCpf: z.string().min(11, "CPF deve ter pelo menos 11 dígitos"),
	photo: z.instanceof(File, { message: "Foto é obrigatória" }),
});

export type EpiSignatureFormType = z.infer<typeof epiSignatureSchema>;

export interface IHandleEpiSignatureFormHook {
	form: ReturnType<typeof useForm<EpiSignatureFormType>>;
	signatureData: any;
	previewPhoto: string | null;
	isSubmitting: boolean;
	handlePhotoCapture: () => void;
	handlePhotoUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
	onSubmit: (data: EpiSignatureFormType) => void;
}

export const useEpiSignatureForm = (): IHandleEpiSignatureFormHook => {
	const { signatureData, updateSignatureData } = useEpiSignaturePage();
	const [previewPhoto, setPreviewPhoto] = useState<string | null>(null);
	const [isSubmitting, setIsSubmitting] = useState(false);

	const form = useForm<EpiSignatureFormType>({
		resolver: zodResolver(epiSignatureSchema),
		defaultValues: {
			personName: "",
			personCpf: "",
		},
	});

	// Preencher formulário com dados já selecionados
	useEffect(() => {
		if (signatureData.personName) {
			form.setValue("personName", signatureData.personName);
		}
		if (signatureData.personCpf) {
			form.setValue("personCpf", signatureData.personCpf);
		}
	}, [signatureData, form]);

	const handlePhotoCapture = async () => {
		try {
			// Implementar captura de foto via câmera
			// Por enquanto, vamos simular com um input file
			const input = document.createElement("input");
			input.type = "file";
			input.accept = "image/*";
			input.capture = "environment"; // Usar câmera traseira
			
			input.onchange = (event) => {
				const file = (event.target as HTMLInputElement).files?.[0];
				if (file) {
					handlePhotoFile(file);
				}
			};
			
			input.click();
		} catch (error) {
			console.error("Erro ao capturar foto:", error);
		}
	};

	const handlePhotoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
		const file = event.target.files?.[0];
		if (file) {
			handlePhotoFile(file);
		}
	};

	const handlePhotoFile = (file: File) => {
		// Validar tipo de arquivo
		if (!file.type.startsWith("image/")) {
			alert("Por favor, selecione um arquivo de imagem válido.");
			return;
		}

		// Validar tamanho (máximo 5MB)
		if (file.size > 5 * 1024 * 1024) {
			alert("A imagem deve ter no máximo 5MB.");
			return;
		}

		// Criar preview
		const reader = new FileReader();
		reader.onload = (e) => {
			setPreviewPhoto(e.target?.result as string);
		};
		reader.readAsDataURL(file);

		// Atualizar formulário
		form.setValue("photo", file);
		form.clearErrors("photo");

		// Atualizar dados globais
		updateSignatureData({ photo: file });
	};

	const onSubmit = async (data: EpiSignatureFormType) => {
		setIsSubmitting(true);
		
		try {
			// Atualizar dados finais
			updateSignatureData({
				personName: data.personName,
				personCpf: data.personCpf,
				photo: data.photo,
			});

			// Simular processamento
			await new Promise(resolve => setTimeout(resolve, 1000));
			
			console.log("Dados da assinatura:", {
				...signatureData,
				...data,
			});

		} catch (error) {
			console.error("Erro ao processar assinatura:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	return {
		form,
		signatureData,
		previewPhoto,
		isSubmitting,
		handlePhotoCapture,
		handlePhotoUpload,
		onSubmit,
	};
};
