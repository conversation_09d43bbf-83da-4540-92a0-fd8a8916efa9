import { useState } from "react";
import { ITerm } from "@/modules/presential/services/requests/terms/find-all";

export function useTermsPage() {
  const [selectedTermId, setSelectedTermId] = useState<number | null>(null);
  const [isCreateTermOpen, setIsCreateTermOpen] = useState(false);
  const [isCreateTagOpen, setIsCreateTagOpen] = useState(false);
  const [isEditTermOpen, setIsEditTermOpen] = useState(false);
  const [editTermData, setEditTermData] = useState({ title: "", idTermTag: "" });

  const handleSelectTermForEdit = (term: ITerm) => {
    setSelectedTermId(term.id);
    setEditTermData({ title: term.fileName, idTermTag: term.idTermTag.toString() });
    setIsEditTermOpen(true);
  };

  return {
    selectedTermId,
    isCreateTermOpen,
    isCreateTagOpen,
    isEditTermOpen,
    editTermData,
    setSelectedTermId,
    setIsCreateTermOpen,
    setIsCreateTagOpen,
    setIsEditTermOpen,
    handleSelectTermForEdit
  };
}