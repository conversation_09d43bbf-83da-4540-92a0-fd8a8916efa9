"use server";

import { IGetDocument } from "@/modules/signature/types/document/get-document.type";
import { handleGetDocumentError } from "@/shared/lib/errors/handle-global-errors.lib";
import { apiInstance } from "@/shared/services/config/api";
import { ApiResponseReturn, IResponseDocumentSigned } from "@/shared/types/requests";
import { DOCUMENT_ROUTES } from "../../endpoints";

export const findDocumentRequest = async ({
	documentToken,
}: {
	documentToken: string;
}): Promise<ApiResponseReturn<IGetDocument> | IResponseDocumentSigned> => {
	try {
		const { data, status } = await apiInstance.get(DOCUMENT_ROUTES.GET_DOCUMENT_TO_SIGN({ documentToken }));

		return {
			success: true,
			data: data,
			status,
		};
	} catch (error) {
		return handleGetDocumentError(error);
	}
};
