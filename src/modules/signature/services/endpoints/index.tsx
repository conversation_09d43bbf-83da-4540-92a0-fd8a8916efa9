export const DOCUMENT_ROUTES = {
	GET_DOCUMENT_TO_SIGN: ({ documentToken }: { documentToken: string }) => `/documento/buscar?token=${documentToken}`,
};

export const SIGNATURE_ROUTES = {
	VALIDATE_SIGNATURE: ({ signatureToken }: { signatureToken: string }) => `/assinatura/validar?chave=${signatureToken}`,
	RUBRIC_UPLOAD: ({ documentId }: { documentId: string }) => `/assinatura/${documentId}/uploadRubrica`,
	SIGN_DOCUMENT: ({ signatureToken }: { signatureToken: string }) => `/assinatura/assinar?token=${signatureToken}`,
};
