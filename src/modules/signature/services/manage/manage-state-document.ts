import { ErrorData } from "@/shared/types/requests";
import { IQueryResponse } from "@/shared/types/requests/query-response.type";
import { IDocumentAlreadySigned, IDocumentData, IGetDocument } from "../../types/document/get-document.type";
import { findDocumentRequest } from "../requests/document/find";

export interface IDocumentRepository {
	getDocument(documentToken: string): Promise<IDocumentData<IGetDocument | IDocumentAlreadySigned | ErrorData>>;
}

export class DocumentManageState implements IDocumentRepository {
	async getDocument(documentToken: string) {
		const response: IQueryResponse = await findDocumentRequest({ documentToken });
		return this.parseDocumentResponse(response);
	}

	private parseDocumentResponse(query: IQueryResponse): IDocumentData<IGetDocument | IDocumentAlreadySigned | ErrorData> {
		if (query?.success && query?.status !== 400) {
			return {
				isDocumentSigned: false,
				data: query.data as IGetDocument,
			};
		}
		if (query?.success && query?.status === 400) {
			return {
				isDocumentSigned: true,
				data: query.data as IDocumentAlreadySigned,
			};
		}

		throw new Error((query.data as ErrorData).message);
	}
}
