"use client";
import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { useEffect } from "react";
import { documentHashCode } from "../../states/document/document-hash.state";

const useDocumentHash = (hash: string) => {
	const setDocumentHashCode = useSetAtom(documentHashCode);
	useEffect(() => {
		setDocumentHashCode(hash);
		return () => setDocumentHashCode(null);
	}, [hash, setDocumentHashCode]);
};

export default useDocumentHash;
