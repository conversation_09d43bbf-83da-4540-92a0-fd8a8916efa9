import { useQuery } from "@tanstack/react-query";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect, useMemo } from "react";
import { SignatureQueryKeys } from "../../query-keys";
import { DocumentManageState } from "../../services/manage/manage-state-document";
import { documentHashCode } from "../../states/document/document-hash.state";
import { signaturePositionAtom } from "../../states/signature/signature-position.state";
import { IGetDocument } from "../../types/document/get-document.type";

export const useGetDocument = () => {
	const documentToken = useAtomValue(documentHashCode);
	const setSignature = useSetAtom(signaturePositionAtom);
	const getCurrentStateDocument = useMemo(() => new DocumentManageState(), []);

	const {
		data: documentData,
		isLoading,
		error,
		isFetching,
	} = useQuery({
		queryKey: SignatureQueryKeys.documentInfo(documentToken!),
		queryFn: () => getCurrentStateDocument.getDocument(documentToken!),
		enabled: !!documentToken,
		retry: false,
	});

	useEffect(() => {
		if (documentData && documentData.isDocumentSigned === false && (documentData.data as IGetDocument)?.rubric) {
			const { rubric } = documentData.data as IGetDocument;
			setSignature({
				x: rubric.coordinates.x,
				y: rubric.coordinates.y,
				page: rubric.pageIndex,
				scale: 1,
			});
		}
	}, [documentData, setSignature]);

	return {
		isLoading,
		isFetching,
		documentData,
		error,
	};
};
