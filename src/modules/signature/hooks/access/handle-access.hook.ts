import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { AccessFormType } from "../../validators/access-form";

export const useHandleAccess = () => {
	const queryClient = useQueryClient();
	const navigate = useRouter();

	useEffect(() => {
		queryClient.removeQueries();
	}, [queryClient]);

	const onAccessDocument = (data: AccessFormType) => {
		// setSubscriptionHashCode(data.documentHash);
		navigate.push(`/assinaturas/${data.documentHash}`);
	};

	return {
		onAccessDocument,
	};
};
