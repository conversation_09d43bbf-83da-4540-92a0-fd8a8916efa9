import { isFullPdfLoadedAtom } from "@/modules/pdf/states/is-full-loaded.state";
import { documentHashCode } from "@/modules/signature/states/document/document-hash.state";
import { documentModalStateOpen } from "@/modules/signature/states/document/document-modal-open.state";
import { modalCreateRubricOpen } from "@/modules/signature/states/rubric/modal-create-rubric-open.state";
import { rubricSvgString } from "@/modules/signature/states/rubric/rubric-svg.state";
import { mainRequisitToSignAtom } from "@/modules/signature/states/signature/main-requisit-to-sign.state";
import { signaturePositionAtom } from "@/modules/signature/states/signature/signature-position.state";
import { usernameAtom } from "@/modules/signature/states/signature/user-name.state";
import { useSetAtom } from "jotai";

export const useResetAllAtoms = () => {
	const setRubricSvgString = useSetAtom(rubricSvgString);
	const setModalCreateRubricOpen = useSetAtom(modalCreateRubricOpen);
	const setDocumentModalStateOpen = useSetAtom(documentModalStateOpen);
	const setDocumentHashCode = useSetAtom(documentHashCode);
	const setUsernameAtom = useSetAtom(usernameAtom);
	const setSignaturePositionAtom = useSetAtom(signaturePositionAtom);
	const setMainRequisitToSignAtom = useSetAtom(mainRequisitToSignAtom);
	const setMainIsFullPdfLoadedAtom = useSetAtom(isFullPdfLoadedAtom);

	const resetAllAtoms = () => {
		setRubricSvgString(null);
		setModalCreateRubricOpen(false);
		setDocumentModalStateOpen(false);
		setDocumentHashCode(null);
		setUsernameAtom(null);
		setSignaturePositionAtom(null);
		setMainRequisitToSignAtom(false);
		setMainIsFullPdfLoadedAtom(false);
	};

	return { resetAllAtoms };
};
