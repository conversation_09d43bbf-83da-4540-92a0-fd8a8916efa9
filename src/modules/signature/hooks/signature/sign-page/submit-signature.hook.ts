import { loadingSigning<PERSON>tom } from "@/modules/signature/states/signature/loading.signing.state";
import { useAtomValue, useSetAtom } from "jotai";
import { useEffect } from "react";
import toast from "react-hot-toast";
import { documentHashCode } from "../../../states/document/document-hash.state";
import { modalCreateRubricOpen } from "../../../states/rubric/modal-create-rubric-open.state";
import { rubricSvgString } from "../../../states/rubric/rubric-svg.state";
import { useSignMutation } from "./signature-mutation.hook";

export const useSubmitSignature = () => {
	const rubricSvg = useAtomValue(rubricSvgString);
	const setLoading = useSetAtom(loadingSigningAtom);
	const setModalCreateRubricOpen = useSetAtom(modalCreateRubricOpen);
	const { mutationSign, isLoading } = useSignMutation();
	const documentHash = useAtomValue(documentHashCode);

	useEffect(() => {
		setLoading(isLoading);
	}, [isLoading, setLoading]);

	const onSubmitSignature = (data: { cpf: string }) => {
		if (!rubricSvg) {
			setModalCreateRubricOpen(true);
			return;
		}

		toast.dismiss();
		toast.loading("Assinando o documento...");

		if (documentHash && !isLoading) {
			mutationSign({
				token: documentHash,
				cpf_cnpj: stripNonDigits(data.cpf),
				rubricPath: rubricSvg,
			});
		}
	};

	return {
		onSubmitSignature,
	};
};

const stripNonDigits = (value: string): string => value.replace(/[^\d]/g, "");
