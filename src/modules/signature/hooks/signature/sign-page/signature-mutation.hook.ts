import { ApiResponseReturn } from "@/shared/types/requests";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAtomValue, useSetAtom } from "jotai";
import toast from "react-hot-toast";
import { SignatureQueryKeys } from "../../../query-keys";
import { signDocumentRequest } from "../../../services/requests/signature/sign";
import { documentHashCode } from "../../../states/document/document-hash.state";
import { rubricSvgString } from "../../../states/rubric/rubric-svg.state";
import { IReturnSignDocumentSucess } from "../../../types/signature/sign-document.type";

export const useSignMutation = () => {
	const queryClient = useQueryClient();
	const documentHash = useAtomValue(documentHashCode);
	const setSignatureSvg = useSetAtom(rubricSvgString);

	const mutationSign = useMutation({
		mutationKey: ["sign"],
		mutationFn: async ({ token, cpf_cnpj, rubricPath }: { token: string; cpf_cnpj: string; rubricPath: string }) => {
			const response = await signDocumentRequest({ signatureToken: token, cpf_cnpj, rubricPath });
			if (response.success) return response as ApiResponseReturn<IReturnSignDocumentSucess>;
			if (response.data?.message === "Credenciais invalidas") {
				throw new Error("As informações inseridas está incorretas");
			}
			throw response.data?.message || "Falha ao assinar o documento";
		},

		onSuccess: data => {
			if (data.success) {
				toast.dismiss();
				toast.success(data.data.message.description);
				queryClient.invalidateQueries({
					queryKey: SignatureQueryKeys.documentInfo(documentHash!),
				});
				setSignatureSvg(null);
			}
		},

		onError: error => {
			toast.dismiss();
			toast.error(error.message);
		},
	});

	return {
		mutationSign: mutationSign.mutate,
		isLoading: mutationSign.isPending,
	};
};
