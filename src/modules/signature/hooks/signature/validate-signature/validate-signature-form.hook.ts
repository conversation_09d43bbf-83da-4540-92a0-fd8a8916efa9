import { hashValidationSignature, hashValidationSignatureType } from "@/modules/signature/validators/validate-signature-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

export const useValidationSingature = () => {
	return useForm<hashValidationSignatureType>({
		resolver: zodResolver(hashValidationSignature),
		defaultValues: {
			hash: "",
		},
	});
};
