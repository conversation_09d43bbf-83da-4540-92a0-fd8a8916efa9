import { JSX } from "react";

export interface IDocumentData {
	title: string;
	description: string;
	finishedDate: string;
}

export interface IRepresentative {
	name: string;
	cpf: string;
}

export interface ISignatoryData {
	name: string;
	cpf_cnpj: string;
	email: string;
	phone: string;
	signDate: string;
	nomenclature: string;
	rubricSVG: string;
	representative?: IRepresentative;
}

export interface IValidDocument {
	documentData: IDocumentData;
	signatoriesData: ISignatoryData[];
}

export interface DocumentValidatedProps {
	data: IValidDocument;
	setIsValid: () => void;
}

export interface InfoRowProps {
	label: string;
	value: string | JSX.Element;
}

export interface SignatoryCardProps {
	signatory: IValidDocument["signatoriesData"][number];
}
