import { Stroke } from "../../hooks/signature/sign-page/signature-canvas.hook";

const SVG_WIDTH = 200;
const SVG_HEIGHT = 100;

export const convertStrokesToSVG = (strokes: Stroke[], canvasWidth: number, canvasHeight: number): string => {
	const paths = strokes
		.map(stroke => {
			if (stroke.length === 0) return "";
			const d = stroke.map((point, index) => (index === 0 ? `M ${point.x} ${point.y}` : `L ${point.x} ${point.y}`)).join(" ");
			return `<path d="${d}" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />`;
		})
		.join("\n");

	return `<svg width="${SVG_WIDTH}" height="${SVG_HEIGHT}" viewBox="0 0 ${canvasWidth} ${canvasHeight}" xmlns="http://www.w3.org/2000/svg">
  ${paths}
</svg>`;
};
