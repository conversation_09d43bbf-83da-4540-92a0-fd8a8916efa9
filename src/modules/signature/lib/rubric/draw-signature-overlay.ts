function loadImageFromSvg(svg: string): Promise<HTMLImageElement> {
	return new Promise((resolve, reject) => {
		const img = new Image();
		img.src = `data:image/svg+xml;base64,${btoa(svg)}`;
		img.onload = () => resolve(img);
		img.onerror = error => reject(error);
	});
}

export async function drawSignatureOverlay(
	context: CanvasRenderingContext2D,
	signature: { x: number; y: number; page: number; scale: number },
	signatureSvg: string,
	scale: number
): Promise<void> {
	let img: HTMLImageElement;
	try {
		img = await loadImageFromSvg(signatureSvg);
	} catch (error) {
		console.error("Erro ao carregar a imagem da assinatura", error);
		return;
	}

	const BASE_SIGNATURE_WIDTH = img.naturalWidth;
	const BASE_SIGNATURE_HEIGHT = img.naturalHeight;

	const scaledCenterX = signature.x * scale;
	const scaledCenterY = signature.y * scale;

	const width = BASE_SIGNATURE_WIDTH * signature.scale * scale;
	const height = BASE_SIGNATURE_HEIGHT * signature.scale * scale;

	const x = scaledCenterX - width / 2;
	const y = scaledCenterY - height / 2;

	context.save();
	context.shadowColor = "rgba(0, 0, 0, 0.2)";
	context.shadowBlur = 8;
	context.shadowOffsetX = 0;
	context.shadowOffsetY = 4;
	context.drawImage(img, x, y, width, height);
	context.restore();

	const gradient = context.createLinearGradient(x, y, x + width, y + height);
	gradient.addColorStop(0, "#ff7e5f");
	gradient.addColorStop(1, "#feb47b");

	const radius = 8 * scale;
	context.save();
	context.setLineDash([6, 4]);
	context.strokeStyle = gradient;
	context.lineWidth = 2;
	context.beginPath();
	context.moveTo(x + radius, y);
	context.lineTo(x + width - radius, y);
	context.quadraticCurveTo(x + width, y, x + width, y + radius);
	context.lineTo(x + width, y + height - radius);
	context.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
	context.lineTo(x + radius, y + height);
	context.quadraticCurveTo(x, y + height, x, y + height - radius);
	context.lineTo(x, y + radius);
	context.quadraticCurveTo(x, y, x + radius, y);
	context.closePath();
	context.stroke();
	context.restore();

	context.save();
	const labelText = "PRÉVIA";
	const padding = 4 * scale;
	context.font = `${16 * scale}px sans-serif`;
	context.textBaseline = "middle";
	context.textAlign = "center";
	const textMetrics = context.measureText(labelText);
	const labelWidth = textMetrics.width + padding * 2;
	const labelHeight = 20 * scale;
	const labelX = x + (width - labelWidth) / 2;
	const labelY = y - labelHeight - 4 * scale;
	context.fillStyle = "rgba(255, 255, 255, 0.9)";
	context.strokeStyle = gradient;
	context.lineWidth = 1;
	const labelRadius = 4 * scale;
	context.beginPath();
	context.moveTo(labelX + labelRadius, labelY);
	context.lineTo(labelX + labelWidth - labelRadius, labelY);
	context.quadraticCurveTo(labelX + labelWidth, labelY, labelX + labelWidth, labelY + labelRadius);
	context.lineTo(labelX + labelWidth, labelY + labelHeight - labelRadius);
	context.quadraticCurveTo(labelX + labelWidth, labelY + labelHeight, labelX + labelWidth - labelRadius, labelY + labelHeight);
	context.lineTo(labelX + labelRadius, labelY + labelHeight);
	context.quadraticCurveTo(labelX, labelY + labelHeight, labelX, labelY + labelHeight - labelRadius);
	context.lineTo(labelX, labelY + labelRadius);
	context.quadraticCurveTo(labelX, labelY, labelX + labelRadius, labelY);
	context.closePath();
	context.fill();
	context.stroke();

	context.fillStyle = "#333";
	context.fillText(labelText, labelX + labelWidth / 2, labelY + labelHeight / 2);
	context.restore();
}
