import { Modal } from "@/shared/components/custom/modal";
import { useAtom, useSet<PERSON>tom } from "jotai";
import { modalCreateRubricOpen } from "../../states/rubric/modal-create-rubric-open.state";
import { rubricSvgString } from "../../states/rubric/rubric-svg.state";
import { SignatureCanvas } from "../document-sign-page/pages/document-not-signed/signature-rubric-canvas";

export const ModalCreateRubric = () => {
	const [isOpen, setIsOpen] = useAtom(modalCreateRubricOpen);
	const setSvgRubric = useSetAtom(rubricSvgString);

	return (
		<Modal className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full" isOpen={isOpen} onRequestClose={() => setIsOpen(false)}>
			<div className="">
				<header className="text-center mb-4">
					<h2 className="text-xl font-semibold">Cadastrar Rúbrica</h2>
					<p className="text-sm text-gray-600">Desenhe sua assinatura abaixo e clique em ‘Salvar’.</p>
				</header>
				<SignatureCanvas
					onCancel={() => setIsOpen(false)}
					onSave={svg => {
						setSvgRubric(svg);
						setIsOpen(false);
					}}
				/>
			</div>

			<p className="text-xs text-gray-500 mt-4 text-center">* Certifique-se de que sua assinatura esteja clara antes de salvar.</p>
		</Modal>
	);
};
