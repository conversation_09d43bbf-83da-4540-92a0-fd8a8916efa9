import PdfViewer from "@/modules/pdf/components/pdf";
import { usePdfActions } from "@/modules/pdf/hooks/actions/pdf-acitions.hook";
import { Button } from "@/shared/components/ui/button";
import { Skeleton } from "@/shared/components/ui/skeleton";
import { useSet<PERSON>tom } from "jotai";
import { CircleX, Download } from "lucide-react";
import { useDocumentModal } from "../../hooks/document/document-modal.hook";
import { mainRequisitToSignAtom } from "../../states/signature/main-requisit-to-sign.state";
import { IGetDocument } from "../../types/document/get-document.type";

export const DocumentModal = ({ data }: { data: IGetDocument }) => {
	const { isOpen, setIsOpen } = useDocumentModal();
	const { downloadPdf } = usePdfActions({ fileName: "documento.pdf" });
	const setMainRequisitToSign = useSetAtom(mainRequisitToSignAtom);

	return (
		<>
			<div className="flex w-full gap-1">
				<Button
					onClick={() => {
						setIsOpen(true);
					}}
					className="w-4/5 h-[60px] bg-gray-800 text-white font-semibold rounded-[10px] hover:bg-gray-900 transition-all duration-300 ease-in-out transform hover:scale-105"
				>
					Ler o documento
				</Button>

				<Button
					onClick={() => {
						downloadPdf();
						setMainRequisitToSign(true);
					}}
					className="w-1/5 h-[60px] bg-gray-800 text-white font-semibold rounded-[10px] hover:bg-gray-900 transition-all duration-300 ease-in-out transform hover:scale-105"
				>
					<Download />
				</Button>
			</div>

			{isOpen && (
				<div className="fixed inset-0 z-50 flex items-center justify-center bg-black/40">
					<main className="w-full sm:relative mx-1  sm:m-3 h-[95vh] sm:h-[80vh] sm:max-w-2xl bg-white overflow-hidden relative flex flex-col justify-between rounded-[10px] md:rounded-lg">
						<header className="w-full h-20 sm:hidden bg-gray-300 sm:bg-transparent px-10 flex items-center justify-center relative">
							<h1 className="text-2xl font-bold text-gray-800">Documento</h1>

							<button className="text-3xl absolute right-4" onClick={() => setIsOpen(false)}>
								<CircleX />
							</button>
						</header>

						<div className="absolute hidden sm:flex top-6 right-5 w-[20px]  z-50 items-center justify-center  h-[20px] sm:h-0">
							<button className="text-5xl bg-gray-300 p-2 rounded-full" onClick={() => setIsOpen(false)}>
								<CircleX />
							</button>
						</div>

						<main className={`w-full bg-white flex-1 flex-grow: h-[95%] sm:h-full`}>
							{data?.documentBuffer?.data ? (
								<PdfViewer id="not-signed-page" buffer={data.documentBuffer.data} />
							) : (
								<div className="w-full h-full flex items-center justify-center bg-black">
									<Skeleton className="w-full h-full" />
								</div>
							)}
						</main>

						{/* {readFullDocument && !signatureSvgString && (
							<footer className="w-full h-20 sm:h-[40px] bg-gray-300 rounded-b-[10px] flex items-center justify-center">
								<div className="items-center text-center  p-2 flex justify-center space-x-2">
									<Checkbox id="terms1-modal" checked={isAcceptModal} onCheckedChange={setIsAcceptModal} />
									<div className="grid gap-1.5 leading-none">
										<label
											htmlFor="terms1-modal"
											className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
										>
											Declaro que li e concordo em assinar o documento
										</label>
									</div>
								</div>
							</footer>
						)} */}
					</main>
				</div>
			)}
		</>
	);
};
