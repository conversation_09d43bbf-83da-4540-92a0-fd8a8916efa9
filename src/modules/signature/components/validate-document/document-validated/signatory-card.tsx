import { SignatoryCardProps } from "@/modules/signature/types/document/ validate-document.type";
import { formatDate } from "@/shared/lib/utils/format-date";
import CreateSvgFromString from "@/shared/lib/utils/string-to-svg";
import { InfoRow } from "./info-row";

export const SignatoryCard: React.FC<SignatoryCardProps> = ({ signatory }) => (
	<div className="bg-gray-50 p-6 rounded-lg shadow-sm space-y-2">
		<InfoRow label="Nome" value={signatory.name} />
		<InfoRow label="CPF/CNPJ" value={signatory.cpf_cnpj} />
		<InfoRow label="Email" value={signatory.email} />
		<InfoRow label="Telefone" value={signatory.phone} />
		<InfoRow label="Data de Assinatura" value={formatDate({ dateString: signatory.signDate })} />
		<InfoRow label="Nomenclatura" value={signatory.nomenclature} />
		{signatory.representative && (
			<>
				<InfoRow label="Representante" value={signatory.representative.name} />
				<InfoRow label="CPF do Representante" value={signatory.representative.cpf} />
			</>
		)}
		<div className="flex justify-between items-center lg:flex-row flex-col lg:items-start">
			<span className="font-semibold text-gray-700">Rubrica:</span>
			<CreateSvgFromString svgString={signatory.rubricSVG} />
		</div>
	</div>
);
