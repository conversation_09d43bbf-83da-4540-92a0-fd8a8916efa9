import { DocumentValidatedProps } from "@/modules/signature/types/document/ validate-document.type";
import { But<PERSON> } from "@/shared/components/ui/button";
import { formatDate } from "@/shared/lib/utils/format-date";
import { CheckCircle } from "lucide-react";
import { InfoRow } from "./info-row";
import { InfoSection } from "./info-section";
import { SignatoryCard } from "./signatory-card";

export const DocumentValidated: React.FC<DocumentValidatedProps> = ({ data, setIsValid }) => {
	return (
		<main className="w-full relative p-10 flex flex-col items-center gap-5">
			<header className="w-full flex justify-between">
				<Button className="gap-2" onClick={setIsValid}>
					Voltar
				</Button>
			</header>

			<div className="w-full max-w-[1000px] mx-auto flex flex-col items-center gap-6">
				<div className="bg-white w-full rounded-sm shadow-sm p-10 flex flex-col items-center gap-6">
					<div className="flex items-center gap-4 text-center">
						<h1 className="text-2xl font-semibold text-gray-800">Documento Validado com Sucesso</h1>
						<CheckCircle className="text-green-500 w-12 h-12" />
					</div>

					<p className="text-gray-600 font-medium max-w-xl text-center">
						As informações do documento e dos signatários estão listadas abaixo.
					</p>

					<InfoSection title="Dados do Documento">
						<InfoRow label="Título" value={data.documentData.title} />
						<InfoRow label="Descrição" value={data.documentData.description} />
						<InfoRow label="Data de Finalização" value={formatDate({ dateString: data.documentData.finishedDate })} />
						<InfoRow label="Total de Signatários" value={data.signatoriesData.length.toString()} />
					</InfoSection>
				</div>

				<div className="bg-white w-full rounded-sm shadow-sm p-10">
					<h1 className="text-2xl font-semibold text-center text-gray-800 mb-6">Informação dos Signatários</h1>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						{data.signatoriesData.map((signatory, index) => (
							<SignatoryCard key={index} signatory={signatory} />
						))}
					</div>
				</div>
			</div>
		</main>
	);
};
