"use client";
import { <PERSON><PERSON> } from "@/shared/components/ui/button";
import { FileCheck } from "lucide-react";
import { useRef } from "react";
import { TitleCard } from "../../cards/card-title";
import { SubTitleCard } from "../../cards/subtitle";

interface UploadDocumentProps {
	file: File | null;
	isDragging: boolean;
	handleFileChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
	handleUpload: (file: File | null) => void;
	handleDrop: (event: React.DragEvent<HTMLDivElement>) => void;
	handleDragOver: (event: React.DragEvent<HTMLDivElement>) => void;
	handleDragLeave: () => void;
	isPending: boolean;
}

export const UploadDocument = ({
	file,
	isDragging,
	handleFileChange,
	handleUpload,
	handleDrop,
	handleDragOver,
	handleDragLeave,
	isPending,
}: UploadDocumentProps) => {
	const fileInputRef = useRef<HTMLInputElement>(null);

	const triggerFileInput = () => fileInputRef.current?.click();

	return (
		<div className="bg-white sm:h-100 flex flex-col items-center justify-center p-8 rounded-lg shadow-lg max-w-xl w-full">
			<header className="text-center w-full mb-1">
				<TitleCard title="Validar Documento" />
				<SubTitleCard title="Para validar um documento, selecione um arquivo PDF no seu dispositivo ou arraste-o para a área abaixo." />
			</header>

			<div
				className={`mb-8 w-full h-[80px] border-2 ${
					isDragging ? "border-green-500" : "border-gray-300"
				} border-dashed rounded-lg flex items-center justify-center cursor-pointer hover:border-gray-400 transition-colors`}
				onDrop={handleDrop}
				onDragOver={handleDragOver}
				onDragLeave={handleDragLeave}
				onClick={triggerFileInput}
			>
				{file ? (
					<p className="text-gray-700 truncate">{file.name}</p>
				) : (
					<p className="text-gray-500 text-center">Arraste o arquivo aqui ou clique para selecionar</p>
				)}
			</div>

			<input ref={fileInputRef} type="file" id="fileInput" accept="application/pdf" onChange={handleFileChange} className="hidden" />

			<Button
				onClick={() => handleUpload(file)}
				disabled={!file || isPending}
				className="w-full h-15 font-bold text-white bg-gray-800 hover:bg-gray-900 flex items-center justify-center gap-2 py-3 transition-colors disabled:bg-gray-500"
			>
				<FileCheck />
				Validar Documento
			</Button>
		</div>
	);
};
