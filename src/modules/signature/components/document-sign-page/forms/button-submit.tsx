"use client";

import { useModalRubric } from "@/modules/signature/hooks/signature/sign-page/modal-rubric.hook";
import { useSignatureActions } from "@/modules/signature/hooks/signature/sign-page/verify-actions.hook";
import { Button } from "@/shared/components/ui/button";
import { forwardRef, useImperativeHandle } from "react";

export type ButtonActionsRef = {
	submitForm: () => void;
};

const ButtonActions = forwardRef<ButtonActionsRef, { onSubmit: () => void }>(({ onSubmit }, ref) => {
	const { submitFormHandler, isDisabled, rubricSvg, signaturePosition, isSignaturePositionDisabled } = useSignatureActions(onSubmit);
	const { handleOpen } = useModalRubric();

	useImperativeHandle(ref, () => ({
		submitForm: submitFormHandler,
	}));

	return (
		<div className="w-full flex flex-col gap-5">
			{signaturePosition && rubricSvg && (
				<Button onClick={handleOpen} className="w-full flex  h-[60px] font-bold bg-gray-800 hover:bg-gray-900">
					Refazer Rúbrica
				</Button>
			)}
			<Button
				disabled={isDisabled || isSignaturePositionDisabled}
				onClick={submitFormHandler}
				variant="default"
				className="w-full h-[60px] font-bold bg-gray-800 hover:bg-gray-900"
			>
				{!rubricSvg ? "Cadastrar Rúbrica" : "Assinar"}
			</Button>
		</div>
	);
});

ButtonActions.displayName = "ButtonActions";

export default ButtonActions;
