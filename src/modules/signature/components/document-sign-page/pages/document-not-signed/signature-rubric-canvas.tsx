import { useSignatureCanvas } from "@/modules/signature/hooks/signature/sign-page/signature-canvas.hook";
import { But<PERSON> } from "@/shared/components/ui/button";
import { CheckCircle, Trash, XCircle } from "lucide-react";
import React from "react";

export interface SignatureCanvasProps {
	onSave: (svg: string) => void;
	onCancel?: () => void;
}

export const SignatureCanvas: React.FC<SignatureCanvasProps> = ({ onSave, onCancel }) => {
	const {
		containerRef,
		canvasRef,
		startDrawingPointer,
		drawPointer,
		endDrawingPointer,
		startDrawingTouch,
		drawTouch,
		endDrawingTouch,
		clearCanvas,
		saveCanvas,
		hasSignature,
	} = useSignatureCanvas({ onSave });

	return (
		<div ref={containerRef} className="w-full gap-4 flex flex-col">
			<canvas
				ref={canvasRef}
				className="border w-[300px] h-[300px] bg-gray-200 border-gray-300 rounded-lg "
				style={{
					touchAction: "none",
					cursor: "url('https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTIkZYxfn9N700-Miduo3_DFmPyrZMLzMpNHQ&s') 16 16, auto",
				}}
				onPointerDown={startDrawingPointer}
				onPointerMove={drawPointer}
				onPointerUp={endDrawingPointer}
				onPointerCancel={endDrawingPointer}
				onTouchStart={startDrawingTouch}
				onTouchMove={drawTouch}
				onTouchEnd={endDrawingTouch}
				onTouchCancel={endDrawingTouch}
			/>

			<div className="flex justify-between items-center gap-2">
				<Button
					variant={"destructive"}
					onClick={clearCanvas}
					className="flex-1 h-12 text-sm font-semibold text-white  flex items-center justify-center gap-1"
				>
					<Trash size={16} /> Limpar
				</Button>
				<Button
					onClick={onCancel}
					className="flex-1 h-12 text-sm font-semibold text-white bg-gray-500 hover:bg-gray-600 flex items-center justify-center gap-1"
				>
					<XCircle size={16} /> Cancelar
				</Button>
				<Button
					disabled={!hasSignature}
					onClick={saveCanvas}
					className="flex-1 h-12 text-sm  disabled:bg-gray-500 font-semibold text-white bg-gray-800 hover:bg-gray-900 flex items-center justify-center gap-1"
				>
					<CheckCircle size={16} /> Salvar
				</Button>
			</div>
		</div>
	);
};
