import { AlertTriangle } from "lucide-react";
import { TitleCard } from "../../../cards/card-title";

export const ErrorGetDocument = ({ error }: { error: Error }) => {
	return (
		<div id="error-page" className="flex-1 relative w-full justify-center  flex items-center   bg-gradient-to-tr from-gray-100  to-gray-200  ">
			<div className="w-3/4 md:w-1/2 lg:w-1/3 h-auto  bg-white p-8 rounded-lg shadow-lg  flex flex-col items-center justify-center ">
				<AlertTriangle size={40} className="text-red-500 mb-6" />
				<TitleCard title={"Ocorreu um erro ao buscar o documento"} />
				<p className="text-lg text-gray-600 text-center mb-4 ">{error?.message ?? "Ocorreu um erro ao buscar o documento"}</p>
			</div>
		</div>
	);
};
