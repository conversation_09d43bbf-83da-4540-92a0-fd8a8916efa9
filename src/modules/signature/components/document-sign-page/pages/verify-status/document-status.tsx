"use client";

import { IDocumentAlreadySigned, IGetDocument } from "@/modules/signature/types/document/get-document.type";
import { DocumentStatusProps } from "@/modules/signature/types/pages/sign-page.type";
import { DocumentSigned } from "../document-already-signed";
import { DocumentNotSigned } from "../document-not-signed";

export const DocumentStatus: React.FC<DocumentStatusProps> = ({ isDocumentSigned, documentData }) => {
	return (
		<div className={`w-full justify-center ${isDocumentSigned ? "items-center" : "items-start"} flex-1  flex-col lg:flex-row flex`}>
			{isDocumentSigned ? (
				<DocumentSigned data={documentData?.data as IDocumentAlreadySigned} />
			) : (
				<DocumentNotSigned data={documentData?.data as IGetDocument} />
			)}
		</div>
	);
};
