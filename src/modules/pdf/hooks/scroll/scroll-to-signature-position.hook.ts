import { SignaturePosition } from "@/modules/signature/states/signature/signature-position.state";
import { useCallback } from "react";

export function useScrollToSignature(containerRef: React.RefObject<HTMLElement | null>, zoom: number): (signature: SignaturePosition | null) => void {
	const scrollToSignature = useCallback(
		(signature: SignaturePosition | null): void => {
			if (!signature) return;
			const container = containerRef.current;
			if (!container) return;

			const { page, x, y } = signature;
			const canvasSelector = `canvas[data-page="${page + 1}"]`;
			const canvas = container.querySelector(canvasSelector) as HTMLCanvasElement | null;
			if (!canvas) return;

			const targetX = canvas.offsetLeft + x * zoom;
			const targetY = canvas.offsetTop + y * zoom;

			const { width: containerWidth, height: containerHeight } = container.getBoundingClientRect();
			const scrollLeft = targetX - containerWidth / 2;
			const scrollTop = targetY - containerHeight / 2;

			container.scrollTo({
				left: scrollLeft,
				top: scrollTop,
				behavior: "smooth",
			});
		},
		[containerRef, zoom]
	);

	return scrollToSignature;
}
