// import { SignaturePosition } from "@/modules/signature/states/signature/signature-position.state";
// import { useEffect } from "react";

// interface AutoScrollParams {
// 	scrollToSignature: (signature: SignaturePosition | null) => void;
// 	signaturePosition: SignaturePosition | null;
// 	rubricSvg: string | null;
// 	containerRef: React.RefObject<HTMLElement | null>;
// 	zoom: number;
// }

// export const useAutoScrollToSignature = ({ scrollToSignature, signaturePosition, rubricSvg, containerRef, zoom }: AutoScrollParams) => {
// 	useEffect(() => {
// 		if (!signaturePosition || !rubricSvg) return;

// 		const container = containerRef.current;
// 		if (!container) return;

// 		const adjustScroll = () => {
// 			const canvasSelector = `canvas[data-page="${signaturePosition.page + 1}"]`;
// 			const canvas = container.querySelector(canvasSelector) as HTMLCanvasElement | null;
// 			if (canvas) scrollToSignature(signaturePosition);
// 		};

// 		const resizeObserver = new ResizeObserver(() => {
// 			adjustScroll();
// 		});

// 		resizeObserver.observe(container);

// 		const intervalId = setInterval(adjustScroll, 300);

// 		return () => {
// 			resizeObserver.disconnect();
// 			clearInterval(intervalId);
// 		};
// 	}, [signaturePosition, rubricSvg, scrollToSignature, containerRef, zoom]);
// };
