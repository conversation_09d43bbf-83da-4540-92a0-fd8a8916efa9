import { useAtomValue } from "jotai";
import { useCallback } from "react";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";
interface UsePdfActionsProps {
	fileName?: string;
}

export const usePdfActions = ({ fileName = "documento.pdf" }: UsePdfActionsProps) => {
	const documentProxy = useAtomValue(pdfDocumentProxy);

	const downloadPdf = useCallback(async () => {
		if (!documentProxy) return;
		const pdfBytes = await documentProxy.getData();
		const blob = new Blob([pdfBytes], { type: "application/pdf" });
		const url = URL.createObjectURL(blob);
		const link = document.createElement("a");
		link.href = url;
		link.download = fileName;
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
		URL.revokeObjectURL(url);
	}, [fileName, documentProxy]);

	const printPdf = useCallback(async () => {
		if (!documentProxy) return;

		const pdfBytes = await documentProxy.getData();
		const blob = new Blob([pdfBytes], { type: "application/pdf" });
		const url = URL.createObjectURL(blob);

		const iframe = document.createElement("iframe");
		iframe.style.display = "none";
		iframe.src = url;
		document.body.appendChild(iframe);

		iframe.onload = () => {
			setTimeout(() => {
				iframe.contentWindow?.print();
				document.body.removeChild(iframe);
				URL.revokeObjectURL(url);
			}, 100);
		};
	}, [documentProxy]);

	return {
		downloadPdf,
		printPdf,
	};
};
