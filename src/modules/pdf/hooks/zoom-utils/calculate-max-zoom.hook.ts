import { PDFDocumentProxy } from "pdfjs-dist";
import { RefObject, useEffect, useState } from "react";

export const useMaxZoom = (pdfDocument: PDFDocumentProxy | null, containerRef: RefObject<HTMLDivElement | null>): number => {
	const [maxZoom, setMaxZoom] = useState<number>(Infinity);

	useEffect(() => {
		if (pdfDocument && containerRef.current) {
			pdfDocument.getPage(1).then(page => {
				const baseViewport = page.getViewport({ scale: 1 });
				const containerWidth = containerRef.current!.clientWidth;
				const computedMaxZoom = (containerWidth * 0.95) / baseViewport.width;
				setMaxZoom(computedMaxZoom);
			});
		}
	}, [pdfDocument, containerRef]);

	return maxZoom;
};
