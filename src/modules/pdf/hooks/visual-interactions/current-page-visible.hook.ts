import { useDebounce } from "@/shared/hooks/utils/debounce";
import { useSet<PERSON>tom } from "jotai";
import { useEffect, useRef } from "react";
import { documentCurrentPage } from "../../states/current-page-document.state";

const THRESHOLDS = Array.from({ length: 101 }, (_, i) => i / 100);

const extractCurrentPage = (entries: IntersectionObserverEntry[]): number | null => {
	let maxRatio = 0;
	let currentPage: number | null = null;

	entries.forEach(entry => {
		if (entry.intersectionRatio > maxRatio) {
			const pageAttr = (entry.target as HTMLElement).getAttribute("data-page");
			const pageNum = Number(pageAttr);
			if (!isNaN(pageNum)) {
				maxRatio = entry.intersectionRatio;
				currentPage = pageNum;
			}
		}
	});

	return currentPage;
};

export const useCurrentVisiblePage = (containerRef: React.RefObject<HTMLElement | null>) => {
	const setDocumentCurrentPage = useSetAtom(documentCurrentPage);
	const latestPage = useRef<number | null>(null);

	const debouncedSetPage = useDebounce(
		() => {
			if (latestPage.current !== null) {
				setDocumentCurrentPage(latestPage.current);
			}
		},
		100,
		[setDocumentCurrentPage]
	);

	useEffect(() => {
		const container = containerRef.current;
		if (!container) return;

		const pages = container.querySelectorAll("[data-page]");
		if (!pages.length) return;

		const observer = new IntersectionObserver(
			entries => {
				const page = extractCurrentPage(entries);
				if (page !== null) {
					latestPage.current = page;
					debouncedSetPage();
				}
			},
			{ threshold: THRESHOLDS }
		);

		pages.forEach(page => observer.observe(page));

		return () => observer.disconnect();
	}, [containerRef, debouncedSetPage]);
};
