import { use<PERSON><PERSON><PERSON><PERSON> } from "jotai";
import { useEffect, useMemo } from "react";
import { PdfService } from "../../services/pdf-service";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";

interface LoadPdfProxyProps {
	id: string;
	buffer: ArrayBuffer;
	service?: PdfService;
}

export const useLoadPdfProxy = ({ id, buffer, service }: LoadPdfProxyProps) => {
	const setDocumentProxy = useSetAtom(pdfDocumentProxy);
	const pdfService = useMemo(() => service || new PdfService(), [service]);

	useEffect(() => {
		async function loadDocument(): Promise<void> {
			if (!buffer) return;

			try {
				const pdf = await pdfService.getPdfDocumentProxy({ id, buffer });
				setDocumentProxy(pdf ?? null);
			} catch (error) {
				console.error("Erro ao carregar o PDF:", error);
			}
		}

		loadDocument();
	}, [id, buffer, pdfService, setDocumentProxy]);

	return {
		clearCache: (): void => pdfService.clearCache(),
	};
};
