import { useCallback, useEffect, useState } from "react";

export function useLoadedPages(id: string, buffer: A<PERSON>yBuffer) {
	const [loadedPages, setLoadedPages] = useState<Set<number>>(new Set());

	const handleRenderComplete = useCallback((pageNumber: number) => {
		setLoadedPages(prev => {
			if (!prev.has(pageNumber)) {
				const newSet = new Set(prev);
				newSet.add(pageNumber);
				return newSet;
			}
			return prev;
		});
	}, []);

	useEffect(() => {
		setLoadedPages(new Set());
	}, [id, buffer]);

	return { loadedPages, handleRenderComplete };
}
