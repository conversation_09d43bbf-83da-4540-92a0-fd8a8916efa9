import { drawSignatureOverlay } from "@/modules/signature/lib/rubric/draw-signature-overlay";
import { SignaturePosition } from "@/modules/signature/states/signature/signature-position.state";
import { useDebounce } from "@/shared/hooks/utils/debounce";
import { PDFDocumentProxy, PDFPageProxy } from "pdfjs-dist";
import { RefObject, useCallback, useEffect, useLayoutEffect, useRef } from "react";
import { useVisibility } from "../visual-interactions/use-canva-visible.hook";

export interface IUsePdfRenderer {
	pdfDocument: PDFDocumentProxy;
	pageNumber: number;
	zoom: number;
	canvasRef: React.RefObject<HTMLCanvasElement | null>;
	onRenderComplete?: () => void;
	signature?: SignaturePosition | null;
	signatureSvg?: string | null;
	forceRenderAllPages?: boolean;
}

export const usePdfRenderer = ({
	pdfDocument,
	pageNumber,
	zoom,
	canvasRef,
	onRenderComplete,
	signature,
	signatureSvg,
	forceRenderAllPages,
}: IUsePdfRenderer) => {
	const renderTaskRef = useRef<ReturnType<PDFPageProxy["render"]> | null>(null);
	const isDrawingSignatureRef = useRef(false);
	const isVisible = useVisibility(canvasRef as RefObject<HTMLElement>, 0.1);

	const cancelRenderTask = () => {
		if (renderTaskRef.current) {
			renderTaskRef.current.cancel();
			renderTaskRef.current = null;
		}
	};

	const renderPage = useCallback(async () => {
		if (!canvasRef.current) return;

		const controller = new AbortController();
		const { signal } = controller;

		try {
			const page = await pdfDocument.getPage(pageNumber);

			if (signal.aborted) return;

			const baseViewport = page.getViewport({ scale: 1 });
			const deviceScale = window.devicePixelRatio || 1;
			const scale = zoom * deviceScale;
			const viewport = page.getViewport({ scale });
			const canvas = canvasRef.current;
			const context = canvas.getContext("2d");
			if (!context) return;

			canvas.width = viewport.width;
			canvas.height = viewport.height;

			if (window.matchMedia("(max-width: 768px)").matches) {
				const containerWidth = canvas.parentElement?.clientWidth || window.innerWidth;
				const aspectRatio = baseViewport.height / baseViewport.width;
				canvas.style.width = "100%";
				canvas.style.height = `${containerWidth * aspectRatio}px`;
			} else {
				canvas.style.width = `${baseViewport.width * zoom}px`;
				canvas.style.height = `${baseViewport.height * zoom}px`;
			}

			if (renderTaskRef.current) renderTaskRef.current.cancel();
			cancelRenderTask();
			const renderTask = page.render({ canvasContext: context, viewport });
			renderTaskRef.current = renderTask;

			await renderTask.promise;

			if (signature && signatureSvg && signature.page + 1 === pageNumber) {
				if (!isDrawingSignatureRef.current) {
					isDrawingSignatureRef.current = true;
					try {
						await drawSignatureOverlay(context, signature, signatureSvg, scale);
					} finally {
						isDrawingSignatureRef.current = false;
					}
				}
			}

			if (onRenderComplete) onRenderComplete();
		} catch (error: unknown) {
			if (error instanceof Error) {
				if (error.name === "RenderingCancelledException" || error.message.includes("Rendering cancelled")) return;
			}
			console.error("Erro ao renderizar a página", error);
		}
	}, [pdfDocument, pageNumber, zoom, onRenderComplete, canvasRef, signature, signatureSvg]);

	useLayoutEffect(() => {
		if (forceRenderAllPages || (isVisible && canvasRef.current && canvasRef.current.clientWidth > 0 && canvasRef.current.clientHeight > 0)) {
			renderPage();
		}
	}, [forceRenderAllPages, renderPage, canvasRef, isVisible]);

	const debouncedRenderPage = useDebounce(renderPage, 200, [renderPage]);
	useEffect(() => {
		window.addEventListener("resize", debouncedRenderPage);
		return () => {
			window.removeEventListener("resize", debouncedRenderPage);
		};
	}, [debouncedRenderPage]);
};
