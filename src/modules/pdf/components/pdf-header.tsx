import { Download, Minus, Plus, Printer } from "lucide-react";
import React from "react";

interface PdfViewerHeaderProps {
	currentPage: number;
	totalPages: number;
	zoom: number;
	maxZoom: number;
	onIncreaseZoom: () => void;
	onDecreaseZoom: () => void;
	onPrint: () => void;
	onDownload: () => void;
}

const PdfViewerHeader: React.FC<PdfViewerHeaderProps> = ({
	currentPage,
	totalPages,
	zoom,
	onIncreaseZoom,
	onDecreaseZoom,
	onPrint,
	onDownload,
	maxZoom,
}) => {
	return (
		<div className="sticky top-0 z-50 hidden lg:flex flex-col lg:flex-row items-center justify-between bg-gradient-to-r from-gray-800 to-gray-700 text-white p-3 shadow-lg">
			<div className="w-1/3 flex"></div>
			<div className="flex items-center justify-center gap-6">
				<div className="flex items-center gap-2">
					<span className="font-bold text-xl">{currentPage}</span>
					<span className="text-sm">/ {totalPages}</span>
				</div>
				<div className="flex items-center gap-3 bg-gray-900 px-3 py-1 rounded">
					<button
						className="p-2 hover:bg-gray-700 transition rounded text-2xl disabled:opacity-50"
						onClick={e => {
							e.preventDefault();
							onDecreaseZoom();
						}}
					>
						<Minus />
					</button>
					<span className="font-semibold">{(zoom * 100).toFixed(0)}%</span>
					<button
						className="p-2 hover:bg-gray-700 transition rounded text-2xl disabled:text-gray-500 disabled:opacity-50"
						onClick={e => {
							e.preventDefault();
							onIncreaseZoom();
						}}
						disabled={zoom >= maxZoom}
					>
						<Plus />
					</button>
				</div>
			</div>
			<div className="flex items-center justify-end w-1/3 gap-4 mt-2 lg:mt-0">
				<button
					className="flex items-center gap-2 bg-gray-900 hover:bg-gray-700 transition px-3 py-2 rounded-lg font-bold disabled:opacity-50"
					onClick={e => {
						e.preventDefault();
						onPrint();
					}}
				>
					<Printer />
				</button>
				<button
					className="flex items-center gap-2 bg-gray-900 hover:bg-gray-700 transition px-3 py-2 rounded-lg font-bold disabled:opacity-50"
					onClick={e => {
						e.preventDefault();
						onDownload();
					}}
				>
					<Download />
				</button>
			</div>
		</div>
	);
};

export default PdfViewerHeader;
