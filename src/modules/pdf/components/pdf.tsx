// PdfViewer.tsx
import { useZoomControl } from "@/modules/pdf/hooks/zoom-utils/pdf-zoom.hook";
import { rubricSvgString } from "@/modules/signature/states/rubric/rubric-svg.state";
import { mainRequisitToSignAtom } from "@/modules/signature/states/signature/main-requisit-to-sign.state";
import { signaturePositionAtom } from "@/modules/signature/states/signature/signature-position.state";
import { useAtom, useAtomValue, useSetAtom } from "jotai";
import React, { useEffect, useRef, useState } from "react";
import { usePdfActions } from "../hooks/actions/pdf-acitions.hook";
import { useLoadedPages } from "../hooks/render/use-loaded-pages.hook";
import { useScrollToSignature } from "../hooks/scroll/scroll-to-signature-position.hook";
import { useCurrentVisiblePage } from "../hooks/visual-interactions/current-page-visible.hook";
import { useEndOfPdfObserver } from "../hooks/visual-interactions/end-page-observer.hook";
import { useMaxZoom } from "../hooks/zoom-utils/calculate-max-zoom.hook";
import { documentCurrentPage } from "../states/current-page-document.state";
import { isFullPdfLoadedAtom } from "../states/is-full-loaded.state";
import { pdfDocumentProxy } from "../states/pdf-proxy.state";
import PdfViewerContent from "./pdf-content";
import PdfViewerHeader from "./pdf-header";

interface PdfViewerProps {
	id: string;
	buffer: ArrayBuffer;
	isModal?: boolean;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ id, buffer, isModal }) => {
	const containerRef = useRef<HTMLDivElement>(null);
	const pdfDocument = useAtomValue(pdfDocumentProxy);
	const totalPages = pdfDocument?.numPages || 1;
	const { loadedPages, handleRenderComplete } = useLoadedPages(id, buffer);
	const currentPage = useAtomValue(documentCurrentPage);
	const { downloadPdf, printPdf } = usePdfActions({ fileName: "document.pdf" });
	const maxZoom = useMaxZoom(pdfDocument, containerRef);
	const { zoom, increaseZoom, decreaseZoom, setZoom } = useZoomControl({ initialZoom: 1, maxZoom });
	const scrollToSignature = useScrollToSignature(containerRef, zoom);
	const setMainRequisitToSign = useSetAtom(mainRequisitToSignAtom);
	const rubricSvg = useAtomValue(rubricSvgString);
	const signaturePosition = useAtomValue(signaturePositionAtom);
	useCurrentVisiblePage(containerRef);
	useEndOfPdfObserver({ containerRef, totalPages });
	const [scrollAtSignature, setScrollAtSignature] = useState(false);
	const [forceRenderAllPages, setForceRenderAllPages] = useState(false);
	const [isFullyLoaded, setIsFullyLoaded] = useAtom(isFullPdfLoadedAtom);

	useEffect(() => {
		if (loadedPages.size === totalPages) {
			setIsFullyLoaded(true);
		}
	}, [loadedPages, totalPages, setIsFullyLoaded]);

	useEffect(() => {
		if (signaturePosition && rubricSvg && !scrollAtSignature) {
			setForceRenderAllPages(true);
			setTimeout(() => {
				scrollToSignature(signaturePosition);
				setScrollAtSignature(true);
				setForceRenderAllPages(false);
			}, 100);
		}
	}, [signaturePosition, rubricSvg, scrollAtSignature, zoom, setZoom, scrollToSignature]);

	if (!pdfDocument) return null;
	return (
		<div className="w-full shadow-lg bg-white p-2 rounded-lg">
			<div
				ref={containerRef}
				className="bg-gray-200 rounded-lg"
				style={{
					position: "relative",
					width: "100%",
					overflow: isFullyLoaded ? "auto" : "hidden",
					height: "87vh",
				}}
			>
				{!isModal && (
					<PdfViewerHeader
						currentPage={currentPage}
						totalPages={totalPages}
						zoom={zoom}
						maxZoom={maxZoom}
						onIncreaseZoom={increaseZoom}
						onDecreaseZoom={decreaseZoom}
						onPrint={printPdf}
						onDownload={() => {
							setMainRequisitToSign(true);
							downloadPdf();
						}}
					/>
				)}
				<PdfViewerContent
					pdfDocument={pdfDocument}
					totalPages={totalPages}
					zoom={zoom}
					onRenderComplete={handleRenderComplete}
					forceRenderAllPages={forceRenderAllPages}
				/>
			</div>
		</div>
	);
};

export default PdfViewer;
