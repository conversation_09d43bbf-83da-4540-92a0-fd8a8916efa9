import { PDFDocumentProxy } from "pdfjs-dist";
import React from "react";
import PdfCanvas from "./pdf-canvas";

interface PdfViewerContentProps {
	pdfDocument: PDFDocumentProxy;
	totalPages: number;
	zoom: number;
	onRenderComplete: (pageNumber: number) => void;
	forceRenderAllPages: boolean;
}

const PdfViewerContent: React.FC<PdfViewerContentProps> = ({ pdfDocument, totalPages, zoom, onRenderComplete, forceRenderAllPages }) => {
	return (
		<>
			{Array.from({ length: totalPages }, (_, index) => (
				<PdfCanvas
					key={index}
					pdfDocument={pdfDocument}
					pageNumber={index + 1}
					zoom={zoom}
					onRenderComplete={() => onRenderComplete(index + 1)}
					forceRenderAllPages={forceRenderAllPages}
				/>
			))}
		</>
	);
};

export default PdfViewerContent;
