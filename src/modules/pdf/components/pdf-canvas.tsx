import { rubricSvgString } from "@/modules/signature/states/rubric/rubric-svg.state";
import { signaturePositionAtom } from "@/modules/signature/states/signature/signature-position.state";
import { useAtomValue } from "jotai";
import { useRef } from "react";
import { usePdfRenderer } from "../hooks/render/render.hook";
import { PdfCanvasProps } from "../types/pdf-canvas-props.type";

const PdfCanvas = ({ pdfDocument, pageNumber, zoom, onRenderComplete, forceRenderAllPages }: PdfCanvasProps) => {
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const signaturePosition = useAtomValue(signaturePositionAtom);
	const rubricSvg = useAtomValue(rubricSvgString);
	usePdfRenderer({
		pdfDocument,
		pageNumber,
		zoom,
		canvasRef,
		onRenderComplete,
		signature: signaturePosition,
		signatureSvg: rubricSvg,
		forceRenderAllPages,
	});
	return <canvas data-page={pageNumber} ref={canvasRef} style={{ display: "block", margin: "1rem auto", maxWidth: "100%" }} />;
};

export default PdfCanvas;
