import { getNavItems } from "@/layout/hooks/header/nav-itens.hook";
import { usernameAtom } from "@/modules/signature/states/signature/user-name.state";
import { useAtomValue } from "jotai";
import { NavItemDesktop } from "./nav-item";

export const NavDesktop = () => {
	const navItems = getNavItems();
	const currentUserName = useAtomValue(usernameAtom);

	return (
		<div className=" w-full hidden lg:flex  items-end justify-end gap-3">
			<nav className="flex items-center">
				<ul className="flex items-center gap-8">
					{navItems.map(item => (
						<NavItemDesktop key={item.href} href={item.href} label={item.label} Icon={item.Icon} />
					))}
				</ul>
			</nav>

			{currentUserName && (
				<div className="flex  items-center">
					<h2 className="text-gray-800 whitespace-nowrap">
						<strong>Bem-vindo</strong>, {currentUserName}
					</h2>
				</div>
			)}
		</div>
	);
};
