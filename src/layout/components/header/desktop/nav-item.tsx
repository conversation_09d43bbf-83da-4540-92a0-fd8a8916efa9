import { INavItem } from "@/layout/types/header/nav-item.type";
import { useResetAllAtoms } from "@/modules/signature/hooks/signature/sign-page/reset-all-atoms.hook";
import Link from "next/link";

export const NavItemDesktop = ({ href, label, Icon }: INavItem) => {
	const { resetAllAtoms } = useResetAllAtoms();

	return (
		<li className="group">
			<Link
				onClick={resetAllAtoms}
				href={href}
				className="text-gray-600 hover:text-gray-900 flex items-center gap-3 transition-colors duration-200"
			>
				<Icon className="w-5 h-5 transition-transform duration-200 group-hover:scale-110" />
				<span className="relative">
					{label}
					<span className="absolute left-0 -bottom-1 w-0 h-[2px] bg-gray-900 transition-all duration-300 group-hover:w-full"></span>
				</span>
			</Link>
		</li>
	);
};
