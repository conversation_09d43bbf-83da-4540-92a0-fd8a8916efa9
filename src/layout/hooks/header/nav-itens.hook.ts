import { INavItem } from "@/layout/types/header/nav-item.type";
import { FileCheck, FilePen, FileSearch } from "lucide-react";

export const getNavItems = (): INavItem[] => [
	{
		href: "/assinaturas/acesso",
		label: "Acessar um Documento",
		Icon: FileSearch,
	},
	{
		href: "/assinaturas/validar",
		label: "Verificar Assinatura",
		Icon: FilePen,
	},
	{
		href: "/assinaturas/validar-documento",
		label: "Validar um Documento",
		Icon: FileCheck,
	},
];
