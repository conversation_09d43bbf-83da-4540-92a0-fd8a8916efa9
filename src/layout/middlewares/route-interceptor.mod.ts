import { NextRequest, NextResponse } from "next/server";
import { routesMap, TypeRoutesEnum } from "../routes/routes";

const PDF_WORKER_FILE_PATH_REGEX = /\/pdfjs\/pdf\.worker\.mjs/;
const DEFAULT_REDIRECT_URL = "/assinaturas/acesso";
const FAVICON_PATH = "/icon.png";
const ROOT_PATH = "/";

export const routeInterceptor = (req: NextRequest) => {
	const { pathname } = req.nextUrl;
	if (PDF_WORKER_FILE_PATH_REGEX.test(pathname) || pathname === FAVICON_PATH) return NextResponse.next();
	const { type, route } = checkRouteType(pathname);
	if (type?.includes(TypeRoutesEnum.PUBLIC) && (route === "not found" || pathname === ROOT_PATH)) {
		return NextResponse.redirect(new URL(DEFAULT_REDIRECT_URL, req.url));
	}

	return NextResponse.next();
};

interface RouteType {
	type: TypeRoutesEnum[];
	route: string;
}

export const checkRouteType = (pathname: string): RouteType => {
	const matchedRoute = Object.keys(routesMap).find(key => new RegExp(key).test(pathname)) ?? "not found";
	const requireType = routesMap[matchedRoute] || [TypeRoutesEnum.PUBLIC];
	return { type: requireType, route: matchedRoute };
};
